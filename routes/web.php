<?php

use App\Http\Controllers\HomeController;
use App\Http\Controllers\Web\AuctionController;
use App\Http\Controllers\Web\BidController;
use App\Http\Controllers\Web\CategoryController;
use App\Http\Controllers\Web\PaymentController;
use App\Http\Controllers\Web\UserController;
use App\Http\Controllers\Web\WatchlistController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

// Public routes
Route::get('/', [HomeController::class, 'index'])->name('home');

// Public auction routes
Route::get('/auctions', [AuctionController::class, 'index'])->name('auctions.index');
Route::get('/auctions/featured', [AuctionController::class, 'featured'])->name('auctions.featured');
Route::get('/auctions/ending-soon', [AuctionController::class, 'endingSoon'])->name('auctions.ending-soon');
Route::get('/auctions/{id}', [AuctionController::class, 'show'])->name('auctions.show');

// Public category routes
Route::get('/categories', [CategoryController::class, 'index'])->name('categories.index');
Route::get('/categories/{slug}', [CategoryController::class, 'show'])->name('categories.show');
Route::get('/categories/{slug}/auctions', [CategoryController::class, 'auctions'])->name('categories.auctions');

// AJAX routes for real-time data (no auth required)
Route::get('/auctions/{auctionId}/highest-bid', [BidController::class, 'highestBid'])->name('bids.highest');
Route::get('/auctions/{auctionId}/recent-bids', [BidController::class, 'recentBids'])->name('bids.recent');
Route::get('/auctions/{auctionId}/winning-bid', [BidController::class, 'winningBid'])->name('bids.winning');

// Authenticated routes
Route::middleware(['auth', 'verified'])->group(function () {
    // Dashboard
    Route::get('/dashboard', [UserController::class, 'dashboard'])->name('dashboard');

    // Auction management
    Route::get('/auctions/create', [AuctionController::class, 'create'])->name('auctions.create');
    Route::post('/auctions', [AuctionController::class, 'store'])->name('auctions.store');
    Route::get('/auctions/{id}/edit', [AuctionController::class, 'edit'])->name('auctions.edit');
    Route::put('/auctions/{id}', [AuctionController::class, 'update'])->name('auctions.update');
    Route::delete('/auctions/{id}', [AuctionController::class, 'destroy'])->name('auctions.destroy');
    Route::post('/auctions/{id}/activate', [AuctionController::class, 'activate'])->name('auctions.activate');
    Route::post('/auctions/{id}/end', [AuctionController::class, 'end'])->name('auctions.end');
    Route::post('/auctions/{id}/images', [AuctionController::class, 'uploadImages'])->name('auctions.upload-images');

    // Bidding
    Route::post('/bids', [BidController::class, 'store'])->name('bids.store');
    Route::delete('/bids/{id}', [BidController::class, 'destroy'])->name('bids.destroy');
    Route::get('/auctions/{auctionId}/bids', [BidController::class, 'index'])->name('bids.index');

    // User auction/bid management
    Route::get('/my/auctions', [UserController::class, 'auctions'])->name('user.auctions');
    Route::get('/my/bids', [UserController::class, 'bids'])->name('user.bids');
    Route::get('/my/won-auctions', [UserController::class, 'wonAuctions'])->name('user.won-auctions');
    Route::get('/my/profile', [UserController::class, 'profile'])->name('user.profile');

    // Watchlist
    Route::get('/watchlist', [WatchlistController::class, 'index'])->name('watchlist.index');
    Route::post('/watchlist', [WatchlistController::class, 'store'])->name('watchlist.store');
    Route::delete('/watchlist/{auctionId}', [WatchlistController::class, 'destroy'])->name('watchlist.destroy');
    Route::get('/watchlist/check/{auctionId}', [WatchlistController::class, 'check'])->name('watchlist.check');
    Route::post('/watchlist/toggle/{auctionId}', [WatchlistController::class, 'toggle'])->name('watchlist.toggle');

    // Payments
    Route::get('/payments', [PaymentController::class, 'index'])->name('payments.index');
    Route::get('/payments/create/{auctionId}', [PaymentController::class, 'create'])->name('payments.create');
    Route::post('/payments', [PaymentController::class, 'store'])->name('payments.store');
    Route::get('/payments/{id}', [PaymentController::class, 'show'])->name('payments.show');
    Route::post('/payments/create-intent', [PaymentController::class, 'createIntent'])->name('payments.create-intent');
    Route::post('/payments/confirm-intent', [PaymentController::class, 'confirmIntent'])->name('payments.confirm-intent');
});

// Webhook routes (no auth required)
Route::post('/payments/webhook', [PaymentController::class, 'webhook'])->name('payments.webhook');

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';

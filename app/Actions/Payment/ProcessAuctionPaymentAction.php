<?php

declare(strict_types=1);

namespace App\Actions\Payment;

use App\Domain\Auction\Repositories\AuctionRepositoryInterface;
use App\Domain\Payment\Models\Payment;
use App\Domain\Payment\Repositories\PaymentRepositoryInterface;
use App\Domain\Payment\ValueObjects\PaymentAmount;
use App\Domain\Payment\ValueObjects\PaymentMethod;
use App\Domain\Shared\Events\EventDispatcher;
use App\Domain\Shared\Exceptions\BusinessRuleException;
use App\Domain\Shared\Exceptions\ValidationException;
use App\Domain\Shared\ValueObjects\Id;
use App\Domain\Shared\ValueObjects\Money;
use App\Domain\User\Repositories\UserRepositoryInterface;
use App\Domain\User\ValueObjects\UserId;
use App\Infrastructure\Services\PaymentService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class ProcessAuctionPaymentAction
{
    public function __construct(
        private PaymentRepositoryInterface $paymentRepository,
        private AuctionRepositoryInterface $auctionRepository,
        private UserRepositoryInterface $userRepository,
        private PaymentService $paymentService,
        private EventDispatcher $eventDispatcher
    ) {}

    public function execute(array $data): Payment
    {
        $this->validate($data);

        return DB::transaction(function () use ($data) {
            // Get and validate user
            $user = $this->userRepository->findByIdOrFail(
                UserId::fromString($data['user_id'])
            );

            // Get and validate auction
            $auction = $this->auctionRepository->findByIdOrFail(
                Id::fromString($data['auction_id'])
            );

            // Validate payment rules
            $this->validatePaymentRules($auction, $user->id(), $data);

            // Calculate payment amount with fees
            $finalPrice = new Money($data['amount'], $data['currency'] ?? 'USD');
            $platformFee = $this->paymentService->calculatePlatformFee($finalPrice);
            $paymentAmount = PaymentAmount::fromGrossAmount($finalPrice, $platformFee->amount() / $finalPrice->amount());

            // Create payment method value object
            $paymentMethod = $this->createPaymentMethod($data['payment_method']);

            // Create payment
            $payment = Payment::create(
                Id::generate(),
                $user->id(),
                $paymentAmount,
                $paymentMethod,
                $auction->id(),
                "Payment for auction: {$auction->title()}",
                [
                    'auction_title' => $auction->title(),
                    'seller_id' => $auction->sellerId()->value(),
                ]
            );

            // Process payment with Stripe
            $paymentIntentData = $this->paymentService->createPaymentIntent(
                $paymentAmount,
                $paymentMethod,
                $user->id(),
                $auction->id(),
                ['type' => 'auction_payment']
            );

            $payment->setPaymentIntentId($paymentIntentData['payment_intent_id']);
            $payment->markAsProcessing();

            // Confirm payment if payment method is provided
            if (isset($data['payment_method_id'])) {
                $confirmationResult = $this->paymentService->confirmPaymentIntent(
                    $paymentIntentData['payment_intent_id'],
                    $data['payment_method_id']
                );

                if ($confirmationResult['status'] === 'succeeded') {
                    $payment->markAsSucceeded();
                } elseif ($confirmationResult['status'] === 'requires_action') {
                    // Handle 3D Secure or other authentication
                    $payment->updateMetadata(['requires_action' => true]);
                }
            }

            // Save payment
            $this->paymentRepository->save($payment);

            // Dispatch events
            $this->eventDispatcher->dispatchAll($payment->releaseEvents());

            return $payment;
        });
    }

    private function validate(array $data): void
    {
        $validator = Validator::make($data, [
            'user_id' => 'required|integer|exists:users,id',
            'auction_id' => 'required|integer|exists:auctions,id',
            'amount' => 'required|numeric|min:0.01|max:1000000',
            'currency' => 'nullable|string|in:USD,EUR,GBP,CAD',
            'payment_method' => 'required|array',
            'payment_method.type' => 'required|string|in:card,bank_transfer,paypal',
            'payment_method_id' => 'sometimes|required|string',
        ]);

        if ($validator->fails()) {
            throw ValidationException::withErrors($validator->errors()->toArray());
        }
    }

    private function validatePaymentRules($auction, UserId $userId, array $data): void
    {
        // Check if auction has ended with this user as winner
        if (!$auction->status()->isEnded()) {
            throw new BusinessRuleException('Auction must be ended to process payment');
        }

        if (!$auction->winnerId() || !$auction->winnerId()->equals($userId)) {
            throw new BusinessRuleException('Only the auction winner can make payment');
        }

        // Check if payment already exists
        $existingPayment = $this->paymentRepository->findForAuctionWinner(
            $auction->id(),
            $userId
        );

        if ($existingPayment && $existingPayment->isSuccessful()) {
            throw BusinessRuleException::paymentAlreadyProcessed($existingPayment->id()->value());
        }

        // Validate payment amount matches final price
        if ($data['amount'] != $auction->finalPrice()->amount()) {
            throw new BusinessRuleException('Payment amount must match auction final price');
        }

        // Check payment deadline
        $paymentDueDays = config('auction.payment.payment_due_days', 3);
        $paymentDeadline = $auction->actualEndTime()->addDays($paymentDueDays);

        if (now() > $paymentDeadline) {
            throw new BusinessRuleException('Payment deadline has passed');
        }
    }

    private function createPaymentMethod(array $methodData): PaymentMethod
    {
        switch ($methodData['type']) {
            case 'card':
                return PaymentMethod::card(
                    $methodData['brand'] ?? 'unknown',
                    $methodData['last_four'] ?? '****',
                    $methodData['exp_month'] ?? 1,
                    $methodData['exp_year'] ?? date('Y') + 1
                );

            case 'bank_transfer':
                return PaymentMethod::bankTransfer(
                    $methodData['bank_name'] ?? 'Unknown Bank',
                    $methodData['account_last4'] ?? '****'
                );

            case 'paypal':
                return PaymentMethod::paypal($methodData['email'] ?? '');

            default:
                throw new ValidationException('Unsupported payment method type');
        }
    }
}

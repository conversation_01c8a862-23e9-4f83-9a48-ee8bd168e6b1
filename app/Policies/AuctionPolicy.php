<?php

declare(strict_types=1);

namespace App\Policies;

use App\Models\Auction;
use App\Models\User;

class AuctionPolicy
{
    /**
     * Determine whether the user can view any auctions.
     */
    public function viewAny(?User $user): bool
    {
        // Anyone can view auctions (including guests)
        return true;
    }

    /**
     * Determine whether the user can view the auction.
     */
    public function view(?User $user, Auction $auction): bool
    {
        // Anyone can view active auctions
        if ($auction->status === 'active') {
            return true;
        }

        // Only authenticated users can view scheduled/ended auctions
        if (!$user) {
            return false;
        }

        // Sellers can view their own auctions regardless of status
        if ($auction->user_id === $user->id) {
            return true;
        }

        // Admins and moderators can view any auction
        if (in_array($user->role, ['admin', 'moderator'])) {
            return true;
        }

        // Users can view ended auctions they participated in
        if ($auction->status === 'ended' && $auction->bids()->where('user_id', $user->id)->exists()) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can create auctions.
     */
    public function create(User $user): bool
    {
        // Must be authenticated and active
        if (!$user->is_active) {
            return false;
        }

        // Must have verified email
        if (!$user->hasVerifiedEmail()) {
            return false;
        }

        // Must have seller privileges
        return in_array($user->role, ['seller', 'premium_seller', 'admin']);
    }

    /**
     * Determine whether the user can update the auction.
     */
    public function update(User $user, Auction $auction): bool
    {
        // Admins can update any auction
        if ($user->role === 'admin') {
            return true;
        }

        // Moderators can update any auction
        if ($user->role === 'moderator') {
            return true;
        }

        // Sellers can only update their own auctions
        if ($auction->user_id !== $user->id) {
            return false;
        }

        // Cannot update auctions that have started
        if ($auction->status !== 'scheduled') {
            return false;
        }

        // Cannot update if there are bids
        if ($auction->bids_count > 0) {
            return false;
        }

        return true;
    }

    /**
     * Determine whether the user can delete the auction.
     */
    public function delete(User $user, Auction $auction): bool
    {
        // Admins can delete any auction
        if ($user->role === 'admin') {
            return true;
        }

        // Sellers can only delete their own scheduled auctions with no bids
        if ($auction->user_id === $user->id && 
            $auction->status === 'scheduled' && 
            $auction->bids_count === 0) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can bid on the auction.
     */
    public function bid(User $user, Auction $auction): bool
    {
        // Must be authenticated and active
        if (!$user->is_active) {
            return false;
        }

        // Must have verified email
        if (!$user->hasVerifiedEmail()) {
            return false;
        }

        // Cannot bid on own auction
        if ($auction->user_id === $user->id) {
            return false;
        }

        // Can only bid on active auctions
        if ($auction->status !== 'active') {
            return false;
        }

        // Check if auction has ended
        if ($auction->end_time->isPast()) {
            return false;
        }

        return true;
    }

    /**
     * Determine whether the user can watch the auction.
     */
    public function watch(User $user, Auction $auction): bool
    {
        // Must be authenticated
        if (!$user) {
            return false;
        }

        // Cannot watch own auction
        if ($auction->user_id === $user->id) {
            return false;
        }

        // Can watch any auction that's not ended
        return $auction->status !== 'ended';
    }

    /**
     * Determine whether the user can feature the auction.
     */
    public function feature(User $user, Auction $auction): bool
    {
        // Only admins can feature auctions
        if ($user->role === 'admin') {
            return true;
        }

        // Premium sellers can feature their own auctions
        if ($user->role === 'premium_seller' && $auction->user_id === $user->id) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can end the auction early.
     */
    public function endEarly(User $user, Auction $auction): bool
    {
        // Admins can end any auction
        if ($user->role === 'admin') {
            return true;
        }

        // Moderators can end any auction
        if ($user->role === 'moderator') {
            return true;
        }

        // Sellers can end their own auctions if there are no bids
        if ($auction->user_id === $user->id && $auction->bids_count === 0) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can extend the auction.
     */
    public function extend(User $user, Auction $auction): bool
    {
        // Only admins can manually extend auctions
        if ($user->role === 'admin') {
            return true;
        }

        // Sellers can extend their own auctions under certain conditions
        if ($auction->user_id === $user->id && 
            $auction->status === 'active' && 
            $auction->end_time->diffInHours(now()) < 1) { // Only in last hour
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can moderate the auction.
     */
    public function moderate(User $user, Auction $auction): bool
    {
        // Admins and moderators can moderate any auction
        return in_array($user->role, ['admin', 'moderator']);
    }

    /**
     * Determine whether the user can view auction analytics.
     */
    public function viewAnalytics(User $user, Auction $auction): bool
    {
        // Admins can view analytics for any auction
        if ($user->role === 'admin') {
            return true;
        }

        // Sellers can view analytics for their own auctions
        if ($auction->user_id === $user->id) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can cancel bids on the auction.
     */
    public function cancelBids(User $user, Auction $auction): bool
    {
        // Only admins and moderators can cancel bids
        return in_array($user->role, ['admin', 'moderator']);
    }
}

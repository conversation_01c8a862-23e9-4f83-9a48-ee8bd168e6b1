<?php

declare(strict_types=1);

namespace App\Policies;

use App\Models\Bid;
use App\Models\User;

class BidPolicy
{
    /**
     * Determine whether the user can view any bids.
     */
    public function viewAny(User $user): bool
    {
        // Admins and moderators can view all bids
        return in_array($user->role, ['admin', 'moderator']);
    }

    /**
     * Determine whether the user can view the bid.
     */
    public function view(User $user, Bid $bid): bool
    {
        // Admins and moderators can view any bid
        if (in_array($user->role, ['admin', 'moderator'])) {
            return true;
        }

        // Users can view their own bids
        if ($bid->user_id === $user->id) {
            return true;
        }

        // Auction sellers can view bids on their auctions
        if ($bid->auction->user_id === $user->id) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can create bids.
     */
    public function create(User $user): bool
    {
        // Must be authenticated and active
        if (!$user->is_active) {
            return false;
        }

        // Must have verified email
        if (!$user->hasVerifiedEmail()) {
            return false;
        }

        // All verified users can bid
        return true;
    }

    /**
     * Determine whether the user can update the bid.
     */
    public function update(User $user, Bid $bid): bool
    {
        // Bids generally cannot be updated once placed
        // Only admins can update bids for moderation purposes
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can delete the bid.
     */
    public function delete(User $user, Bid $bid): bool
    {
        // Only admins can delete bids
        if ($user->role === 'admin') {
            return true;
        }

        // Users can delete their own bids only if they are not the winning bid
        // and the auction is still active
        if ($bid->user_id === $user->id && 
            !$bid->is_winning && 
            $bid->auction->status === 'active') {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can cancel the bid.
     */
    public function cancel(User $user, Bid $bid): bool
    {
        // Admins and moderators can cancel any bid
        if (in_array($user->role, ['admin', 'moderator'])) {
            return true;
        }

        // Users can cancel their own bids under certain conditions
        if ($bid->user_id === $user->id) {
            // Cannot cancel if it's the winning bid
            if ($bid->is_winning) {
                return false;
            }

            // Cannot cancel if auction has ended
            if ($bid->auction->status !== 'active') {
                return false;
            }

            // Can cancel within 5 minutes of placing the bid
            if ($bid->created_at->diffInMinutes(now()) <= 5) {
                return true;
            }
        }

        return false;
    }

    /**
     * Determine whether the user can invalidate the bid.
     */
    public function invalidate(User $user, Bid $bid): bool
    {
        // Only admins and moderators can invalidate bids
        return in_array($user->role, ['admin', 'moderator']);
    }

    /**
     * Determine whether the user can validate the bid.
     */
    public function validate(User $user, Bid $bid): bool
    {
        // Only admins and moderators can validate bids
        return in_array($user->role, ['admin', 'moderator']);
    }

    /**
     * Determine whether the user can view bid history for an auction.
     */
    public function viewHistory(User $user, $auction): bool
    {
        // Admins and moderators can view any bid history
        if (in_array($user->role, ['admin', 'moderator'])) {
            return true;
        }

        // Auction sellers can view bid history for their auctions
        if ($auction->user_id === $user->id) {
            return true;
        }

        // Users can view bid history for auctions they've bid on
        if ($auction->bids()->where('user_id', $user->id)->exists()) {
            return true;
        }

        // For ended auctions, anyone can view basic bid history
        if ($auction->status === 'ended') {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can place a proxy bid.
     */
    public function proxyBid(User $user): bool
    {
        // Must be authenticated and active
        if (!$user->is_active) {
            return false;
        }

        // Must have verified email
        if (!$user->hasVerifiedEmail()) {
            return false;
        }

        // All verified users can place proxy bids
        return true;
    }

    /**
     * Determine whether the user can view bid analytics.
     */
    public function viewAnalytics(User $user, Bid $bid): bool
    {
        // Admins can view analytics for any bid
        if ($user->role === 'admin') {
            return true;
        }

        // Users can view analytics for their own bids
        if ($bid->user_id === $user->id) {
            return true;
        }

        // Auction sellers can view bid analytics for their auctions
        if ($bid->auction->user_id === $user->id) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can retract the bid.
     */
    public function retract(User $user, Bid $bid): bool
    {
        // Users can only retract their own bids
        if ($bid->user_id !== $user->id) {
            return false;
        }

        // Cannot retract if auction has ended
        if ($bid->auction->status !== 'active') {
            return false;
        }

        // Cannot retract winning bid in the last hour of auction
        if ($bid->is_winning && $bid->auction->end_time->diffInHours(now()) < 1) {
            return false;
        }

        // Can retract within 1 hour of placing the bid
        return $bid->created_at->diffInHours(now()) <= 1;
    }

    /**
     * Determine whether the user can flag the bid as suspicious.
     */
    public function flag(User $user, Bid $bid): bool
    {
        // Authenticated users can flag suspicious bids
        if (!$user) {
            return false;
        }

        // Cannot flag own bids
        if ($bid->user_id === $user->id) {
            return false;
        }

        // Auction sellers can flag bids on their auctions
        if ($bid->auction->user_id === $user->id) {
            return true;
        }

        // Other bidders can flag suspicious bids
        if ($bid->auction->bids()->where('user_id', $user->id)->exists()) {
            return true;
        }

        return false;
    }
}

<?php

declare(strict_types=1);

namespace App\Policies;

use App\Models\Payment;
use App\Models\User;

class PaymentPolicy
{
    /**
     * Determine whether the user can view any payments.
     */
    public function viewAny(User $user): bool
    {
        // Admins and moderators can view all payments
        return in_array($user->role, ['admin', 'moderator']);
    }

    /**
     * Determine whether the user can view the payment.
     */
    public function view(User $user, Payment $payment): bool
    {
        // Admins and moderators can view any payment
        if (in_array($user->role, ['admin', 'moderator'])) {
            return true;
        }

        // Users can view their own payments
        if ($payment->user_id === $user->id) {
            return true;
        }

        // Auction sellers can view payments for their auctions
        if ($payment->auction && $payment->auction->user_id === $user->id) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can create payments.
     */
    public function create(User $user): bool
    {
        // Must be authenticated and active
        if (!$user->is_active) {
            return false;
        }

        // Must have verified email
        if (!$user->hasVerifiedEmail()) {
            return false;
        }

        // All verified users can create payments
        return true;
    }

    /**
     * Determine whether the user can update the payment.
     */
    public function update(User $user, Payment $payment): bool
    {
        // Only admins can update payments
        if ($user->role === 'admin') {
            return true;
        }

        // Users can update their own pending payments (limited fields)
        if ($payment->user_id === $user->id && $payment->status === 'pending') {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can delete the payment.
     */
    public function delete(User $user, Payment $payment): bool
    {
        // Only admins can delete payments
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can cancel the payment.
     */
    public function cancel(User $user, Payment $payment): bool
    {
        // Admins can cancel any payment
        if ($user->role === 'admin') {
            return true;
        }

        // Users can cancel their own pending or processing payments
        if ($payment->user_id === $user->id && 
            in_array($payment->status, ['pending', 'processing'])) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can refund the payment.
     */
    public function refund(User $user, Payment $payment): bool
    {
        // Only admins and moderators can process refunds
        if (!in_array($user->role, ['admin', 'moderator'])) {
            return false;
        }

        // Can only refund completed payments
        if ($payment->status !== 'completed') {
            return false;
        }

        // Cannot refund already refunded payments
        if ($payment->status === 'refunded') {
            return false;
        }

        return true;
    }

    /**
     * Determine whether the user can retry the payment.
     */
    public function retry(User $user, Payment $payment): bool
    {
        // Users can retry their own failed payments
        if ($payment->user_id === $user->id && $payment->status === 'failed') {
            return true;
        }

        // Admins can retry any failed payment
        if ($user->role === 'admin' && $payment->status === 'failed') {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can view payment details.
     */
    public function viewDetails(User $user, Payment $payment): bool
    {
        // Admins can view full details of any payment
        if ($user->role === 'admin') {
            return true;
        }

        // Users can view details of their own payments
        if ($payment->user_id === $user->id) {
            return true;
        }

        // Auction sellers can view basic details of payments for their auctions
        if ($payment->auction && $payment->auction->user_id === $user->id) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can download payment receipts.
     */
    public function downloadReceipt(User $user, Payment $payment): bool
    {
        // Users can download receipts for their own completed payments
        if ($payment->user_id === $user->id && $payment->status === 'completed') {
            return true;
        }

        // Admins can download any receipt
        if ($user->role === 'admin') {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can view payment analytics.
     */
    public function viewAnalytics(User $user, Payment $payment): bool
    {
        // Admins can view analytics for any payment
        if ($user->role === 'admin') {
            return true;
        }

        // Users can view analytics for their own payments
        if ($payment->user_id === $user->id) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can dispute the payment.
     */
    public function dispute(User $user, Payment $payment): bool
    {
        // Users can dispute their own completed payments within 30 days
        if ($payment->user_id === $user->id && 
            $payment->status === 'completed' &&
            $payment->processed_at &&
            $payment->processed_at->diffInDays(now()) <= 30) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can resolve payment disputes.
     */
    public function resolveDispute(User $user, Payment $payment): bool
    {
        // Only admins and moderators can resolve disputes
        return in_array($user->role, ['admin', 'moderator']);
    }

    /**
     * Determine whether the user can mark payment as fraudulent.
     */
    public function markFraudulent(User $user, Payment $payment): bool
    {
        // Only admins and moderators can mark payments as fraudulent
        return in_array($user->role, ['admin', 'moderator']);
    }

    /**
     * Determine whether the user can export payment data.
     */
    public function export(User $user): bool
    {
        // Only admins can export payment data
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can view payment gateway logs.
     */
    public function viewGatewayLogs(User $user, Payment $payment): bool
    {
        // Only admins can view gateway logs
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can manually process payments.
     */
    public function manualProcess(User $user, Payment $payment): bool
    {
        // Only admins can manually process payments
        if ($user->role !== 'admin') {
            return false;
        }

        // Can only manually process pending or failed payments
        return in_array($payment->status, ['pending', 'failed']);
    }

    /**
     * Determine whether the user can view payment statistics.
     */
    public function viewStatistics(User $user): bool
    {
        // Admins and moderators can view payment statistics
        return in_array($user->role, ['admin', 'moderator']);
    }
}

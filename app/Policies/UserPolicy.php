<?php

declare(strict_types=1);

namespace App\Policies;

use App\Models\User;

class UserPolicy
{
    /**
     * Determine whether the user can view any users.
     */
    public function viewAny(User $user): bool
    {
        // Only admins and moderators can view user lists
        return in_array($user->role, ['admin', 'moderator']);
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, User $model): bool
    {
        // Admins and moderators can view any user
        if (in_array($user->role, ['admin', 'moderator'])) {
            return true;
        }

        // Users can view their own profile
        if ($user->id === $model->id) {
            return true;
        }

        // Users can view basic info of other users they've interacted with
        // (e.g., in auctions, bids)
        return $this->hasInteractedWith($user, $model);
    }

    /**
     * Determine whether the user can create users.
     */
    public function create(User $user): bool
    {
        // Only admins can create users through admin panel
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, User $model): bool
    {
        // Admins can update any user
        if ($user->role === 'admin') {
            return true;
        }

        // Users can update their own profile
        if ($user->id === $model->id) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, User $model): bool
    {
        // Admins can delete users (except themselves)
        if ($user->role === 'admin' && $user->id !== $model->id) {
            return true;
        }

        // Users can delete their own account
        if ($user->id === $model->id) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, User $model): bool
    {
        // Only admins can restore deleted users
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, User $model): bool
    {
        // Only admins can permanently delete users
        return $user->role === 'admin';
    }

    /**
     * Determine whether the user can change roles.
     */
    public function changeRole(User $user, User $model): bool
    {
        // Only admins can change user roles
        if ($user->role !== 'admin') {
            return false;
        }

        // Admins cannot change their own role
        if ($user->id === $model->id) {
            return false;
        }

        return true;
    }

    /**
     * Determine whether the user can suspend other users.
     */
    public function suspend(User $user, User $model): bool
    {
        // Admins and moderators can suspend users
        if (!in_array($user->role, ['admin', 'moderator'])) {
            return false;
        }

        // Cannot suspend themselves
        if ($user->id === $model->id) {
            return false;
        }

        // Moderators cannot suspend admins
        if ($user->role === 'moderator' && $model->role === 'admin') {
            return false;
        }

        return true;
    }

    /**
     * Determine whether the user can unsuspend other users.
     */
    public function unsuspend(User $user, User $model): bool
    {
        // Same rules as suspend
        return $this->suspend($user, $model);
    }

    /**
     * Determine whether the user can verify email addresses.
     */
    public function verifyEmail(User $user, User $model): bool
    {
        // Admins and moderators can verify any email
        if (in_array($user->role, ['admin', 'moderator'])) {
            return true;
        }

        // Users can verify their own email
        if ($user->id === $model->id) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can view user statistics.
     */
    public function viewStatistics(User $user, User $model): bool
    {
        // Admins can view any user's statistics
        if ($user->role === 'admin') {
            return true;
        }

        // Users can view their own statistics
        if ($user->id === $model->id) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can view user activity logs.
     */
    public function viewActivityLogs(User $user, User $model): bool
    {
        // Admins and moderators can view any user's activity logs
        if (in_array($user->role, ['admin', 'moderator'])) {
            return true;
        }

        // Users can view their own activity logs
        if ($user->id === $model->id) {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can impersonate other users.
     */
    public function impersonate(User $user, User $model): bool
    {
        // Only admins can impersonate users
        if ($user->role !== 'admin') {
            return false;
        }

        // Cannot impersonate themselves
        if ($user->id === $model->id) {
            return false;
        }

        // Cannot impersonate other admins
        if ($model->role === 'admin') {
            return false;
        }

        return true;
    }

    /**
     * Determine whether the user can manage user permissions.
     */
    public function managePermissions(User $user, User $model): bool
    {
        // Only admins can manage permissions
        return $user->role === 'admin' && $user->id !== $model->id;
    }

    /**
     * Determine whether the user can export user data.
     */
    public function exportData(User $user, User $model): bool
    {
        // Admins can export any user's data
        if ($user->role === 'admin') {
            return true;
        }

        // Users can export their own data
        if ($user->id === $model->id) {
            return true;
        }

        return false;
    }

    /**
     * Check if two users have interacted (through auctions, bids, etc.)
     */
    private function hasInteractedWith(User $user, User $model): bool
    {
        // Check if they've been in the same auction (one as seller, other as bidder)
        $userAuctions = $user->auctions()->pluck('id');
        $modelBids = $model->bids()->whereIn('auction_id', $userAuctions)->exists();
        
        if ($modelBids) {
            return true;
        }

        // Check the reverse (model as seller, user as bidder)
        $modelAuctions = $model->auctions()->pluck('id');
        $userBids = $user->bids()->whereIn('auction_id', $modelAuctions)->exists();
        
        return $userBids;
    }
}

<?php

declare(strict_types=1);

namespace App\Providers;

use App\Models\Auction;
use App\Models\Bid;
use App\Models\Payment;
use App\Models\User;
use App\Policies\AuctionPolicy;
use App\Policies\BidPolicy;
use App\Policies\PaymentPolicy;
use App\Policies\UserPolicy;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Gate;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        Auction::class => AuctionPolicy::class,
        Bid::class => BidPolicy::class,
        Payment::class => PaymentPolicy::class,
        User::class => UserPolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        // Register policies
        $this->registerPolicies();

        // Define additional gates
        $this->defineGates();
    }

    /**
     * Define custom authorization gates.
     */
    private function defineGates(): void
    {
        // Admin access gate
        Gate::define('admin-access', function (User $user) {
            return $user->role === 'admin';
        });

        // Moderator access gate
        Gate::define('moderator-access', function (User $user) {
            return in_array($user->role, ['admin', 'moderator']);
        });

        // Seller access gate
        Gate::define('seller-access', function (User $user) {
            return in_array($user->role, ['admin', 'seller', 'premium_seller']);
        });

        // Premium seller access gate
        Gate::define('premium-seller-access', function (User $user) {
            return in_array($user->role, ['admin', 'premium_seller']);
        });

        // Verified user gate
        Gate::define('verified-user', function (User $user) {
            return $user->hasVerifiedEmail() && $user->is_active;
        });

        // Active user gate
        Gate::define('active-user', function (User $user) {
            return $user->is_active;
        });

        // Can create auctions gate
        Gate::define('create-auctions', function (User $user) {
            return $user->hasVerifiedEmail() && 
                   $user->is_active && 
                   in_array($user->role, ['admin', 'seller', 'premium_seller']);
        });

        // Can bid gate
        Gate::define('can-bid', function (User $user) {
            return $user->hasVerifiedEmail() && $user->is_active;
        });

        // Can access admin panel gate
        Gate::define('access-admin-panel', function (User $user) {
            return $user->is_active && in_array($user->role, ['admin', 'moderator']);
        });

        // Can manage users gate
        Gate::define('manage-users', function (User $user) {
            return $user->role === 'admin';
        });

        // Can manage auctions gate
        Gate::define('manage-auctions', function (User $user) {
            return in_array($user->role, ['admin', 'moderator']);
        });

        // Can manage payments gate
        Gate::define('manage-payments', function (User $user) {
            return in_array($user->role, ['admin', 'moderator']);
        });

        // Can view analytics gate
        Gate::define('view-analytics', function (User $user) {
            return in_array($user->role, ['admin', 'moderator']);
        });

        // Can export data gate
        Gate::define('export-data', function (User $user) {
            return $user->role === 'admin';
        });

        // Can feature auctions gate
        Gate::define('feature-auctions', function (User $user) {
            return in_array($user->role, ['admin', 'premium_seller']);
        });

        // Can moderate content gate
        Gate::define('moderate-content', function (User $user) {
            return in_array($user->role, ['admin', 'moderator']);
        });

        // Can process refunds gate
        Gate::define('process-refunds', function (User $user) {
            return in_array($user->role, ['admin', 'moderator']);
        });

        // Can view system logs gate
        Gate::define('view-system-logs', function (User $user) {
            return $user->role === 'admin';
        });

        // Can manage system settings gate
        Gate::define('manage-system-settings', function (User $user) {
            return $user->role === 'admin';
        });

        // Can impersonate users gate
        Gate::define('impersonate-users', function (User $user) {
            return $user->role === 'admin';
        });

        // Can send system notifications gate
        Gate::define('send-system-notifications', function (User $user) {
            return in_array($user->role, ['admin', 'moderator']);
        });

        // Can manage categories gate
        Gate::define('manage-categories', function (User $user) {
            return in_array($user->role, ['admin', 'moderator']);
        });

        // Can view financial reports gate
        Gate::define('view-financial-reports', function (User $user) {
            return $user->role === 'admin';
        });

        // Can manage site maintenance gate
        Gate::define('manage-site-maintenance', function (User $user) {
            return $user->role === 'admin';
        });

        // Super admin gate (for critical operations)
        Gate::define('super-admin', function (User $user) {
            return $user->role === 'admin' && $user->email === config('app.super_admin_email');
        });

        // Can access API gate
        Gate::define('access-api', function (User $user) {
            return $user->is_active && $user->hasVerifiedEmail();
        });

        // Can upload files gate
        Gate::define('upload-files', function (User $user) {
            return $user->is_active && $user->hasVerifiedEmail();
        });

        // Can delete own content gate
        Gate::define('delete-own-content', function (User $user) {
            return $user->is_active;
        });

        // Can report content gate
        Gate::define('report-content', function (User $user) {
            return $user->is_active;
        });

        // Can contact support gate
        Gate::define('contact-support', function (User $user) {
            return $user->is_active;
        });
    }
}

<?php

declare(strict_types=1);

namespace App\Providers;

use App\Domain\Auction\Events\AuctionCreated;
use App\Domain\Auction\Events\AuctionEnded;
use App\Domain\Auction\Events\AuctionStarted;
use App\Domain\Auction\Events\BidPlaced;
use App\Domain\Auction\Events\BuyoutExecuted;
use App\Domain\Payment\Events\PaymentCompleted;
use App\Domain\Payment\Events\PaymentFailed;
use App\Domain\Payment\Events\PaymentInitiated;
use App\Domain\User\Events\ProfileUpdated;
use App\Domain\User\Events\UserRegistered;
use App\Listeners\Auction\NotifyWatchersOnAuctionEnd;
use App\Listeners\Auction\ProcessAuctionEnd;
use App\Listeners\Auction\SendAuctionCreatedNotification;
use App\Listeners\Auction\SendBidNotifications;
use App\Listeners\Auction\UpdateAuctionStatistics;
use App\Listeners\Payment\ProcessPaymentCompletion;
use App\Listeners\Payment\SendPaymentFailedNotification;
use App\Listeners\Payment\SendPaymentReceiptEmail;
use App\Listeners\User\SendWelcomeEmail;
use App\Listeners\User\UpdateUserProfile;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event to listener mappings for the application.
     *
     * @var array<class-string, array<int, class-string>>
     */
    protected $listen = [
        // User Events
        UserRegistered::class => [
            SendWelcomeEmail::class,
        ],
        
        ProfileUpdated::class => [
            UpdateUserProfile::class,
        ],
        
        // Auction Events
        AuctionCreated::class => [
            SendAuctionCreatedNotification::class,
            UpdateAuctionStatistics::class,
        ],
        
        AuctionStarted::class => [
            // Add listeners for auction start
        ],
        
        AuctionEnded::class => [
            ProcessAuctionEnd::class,
            NotifyWatchersOnAuctionEnd::class,
            UpdateAuctionStatistics::class,
        ],
        
        BidPlaced::class => [
            SendBidNotifications::class,
            UpdateAuctionStatistics::class,
        ],
        
        BuyoutExecuted::class => [
            ProcessAuctionEnd::class,
            SendBidNotifications::class,
        ],
        
        // Payment Events
        PaymentInitiated::class => [
            // Add listeners for payment initiation
        ],
        
        PaymentCompleted::class => [
            ProcessPaymentCompletion::class,
            SendPaymentReceiptEmail::class,
        ],
        
        PaymentFailed::class => [
            SendPaymentFailedNotification::class,
        ],
    ];

    /**
     * Register any events for your application.
     */
    public function boot(): void
    {
        //
    }

    /**
     * Determine if events and listeners should be automatically discovered.
     */
    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}

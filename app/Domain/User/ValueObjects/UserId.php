<?php

declare(strict_types=1);

namespace App\Domain\User\ValueObjects;

use App\Domain\Shared\ValueObjects\Id;

class UserId extends Id
{
    public static function fromString(string $value): self
    {
        if (!is_numeric($value)) {
            throw new \InvalidArgumentException('User ID must be numeric');
        }

        return new self((int) $value);
    }

    public static function fromUser(\Illuminate\Contracts\Auth\Authenticatable $user): self
    {
        return new self($user->getAuthIdentifier());
    }

    public function toEloquentId(): int
    {
        return $this->value();
    }
}

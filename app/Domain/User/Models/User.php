<?php

declare(strict_types=1);

namespace App\Domain\User\Models;

use App\Domain\Shared\Models\AggregateRoot;
use App\Domain\Shared\ValueObjects\Timestamp;
use App\Domain\User\Events\UserRegistered;
use App\Domain\User\Events\ProfileUpdated;
use App\Domain\User\ValueObjects\Email;
use App\Domain\User\ValueObjects\PhoneNumber;
use App\Domain\User\ValueObjects\UserId;
use App\Domain\User\ValueObjects\UserRole;

class User extends AggregateRoot
{
    private UserId $id;
    private string $name;
    private Email $email;
    private ?Timestamp $emailVerifiedAt;
    private string $password;
    private ?string $avatar;
    private ?PhoneNumber $phone;
    private UserRole $role;
    private bool $isActive;
    private ?Timestamp $lastLoginAt;
    private Timestamp $createdAt;
    private Timestamp $updatedAt;

    public function __construct(
        UserId $id,
        string $name,
        Email $email,
        string $password,
        UserRole $role = null
    ) {
        $this->id = $id;
        $this->name = $name;
        $this->email = $email;
        $this->password = $password;
        $this->role = $role ?? UserRole::user();
        $this->isActive = true;
        $this->emailVerifiedAt = null;
        $this->avatar = null;
        $this->phone = null;
        $this->lastLoginAt = null;
        $this->createdAt = Timestamp::now();
        $this->updatedAt = Timestamp::now();

        $this->recordEvent(new UserRegistered($this->id, [
            'name' => $this->name,
            'email' => $this->email->value(),
            'role' => $this->role->value(),
        ]));
    }

    public static function register(
        UserId $id,
        string $name,
        Email $email,
        string $password,
        UserRole $role = null
    ): self {
        return new self($id, $name, $email, $password, $role);
    }

    public function id(): UserId
    {
        return $this->id;
    }

    public function name(): string
    {
        return $this->name;
    }

    public function email(): Email
    {
        return $this->email;
    }

    public function role(): UserRole
    {
        return $this->role;
    }

    public function isActive(): bool
    {
        return $this->isActive;
    }

    public function isEmailVerified(): bool
    {
        return $this->emailVerifiedAt !== null;
    }

    public function avatar(): ?string
    {
        return $this->avatar;
    }

    public function phone(): ?PhoneNumber
    {
        return $this->phone;
    }

    public function lastLoginAt(): ?Timestamp
    {
        return $this->lastLoginAt;
    }

    public function createdAt(): Timestamp
    {
        return $this->createdAt;
    }

    public function updatedAt(): Timestamp
    {
        return $this->updatedAt;
    }

    public function password(): string
    {
        return $this->password;
    }

    public function emailVerifiedAt(): ?Timestamp
    {
        return $this->emailVerifiedAt;
    }

    public function updateProfile(
        string $name,
        ?PhoneNumber $phone = null,
        ?string $avatar = null
    ): void {
        $oldName = $this->name;
        $oldPhone = $this->phone;
        $oldAvatar = $this->avatar;

        $this->name = $name;
        $this->phone = $phone;
        $this->avatar = $avatar;
        $this->updatedAt = Timestamp::now();

        $this->recordEvent(new ProfileUpdated($this->id, [
            'old_name' => $oldName,
            'new_name' => $name,
            'old_phone' => $oldPhone?->value(),
            'new_phone' => $phone?->value(),
            'old_avatar' => $oldAvatar,
            'new_avatar' => $avatar,
        ]));
    }

    public function verifyEmail(): void
    {
        if ($this->isEmailVerified()) {
            return;
        }

        $this->emailVerifiedAt = Timestamp::now();
        $this->updatedAt = Timestamp::now();
    }

    public function recordLogin(): void
    {
        $this->lastLoginAt = Timestamp::now();
        $this->updatedAt = Timestamp::now();
    }

    public function deactivate(): void
    {
        $this->isActive = false;
        $this->updatedAt = Timestamp::now();
    }

    public function activate(): void
    {
        $this->isActive = true;
        $this->updatedAt = Timestamp::now();
    }

    public function changeRole(UserRole $newRole): void
    {
        $oldRole = $this->role;
        $this->role = $newRole;
        $this->updatedAt = Timestamp::now();

        $this->recordEvent(new ProfileUpdated($this->id, [
            'old_role' => $oldRole->value(),
            'new_role' => $newRole->value(),
        ]));
    }

    public function canCreateAuctions(): bool
    {
        return $this->isActive && $this->role->canCreateAuctions();
    }

    public function canModerateAuctions(): bool
    {
        return $this->isActive && $this->role->canModerateAuctions();
    }

    public function canAccessAdminPanel(): bool
    {
        return $this->isActive && $this->role->canAccessAdminPanel();
    }
}

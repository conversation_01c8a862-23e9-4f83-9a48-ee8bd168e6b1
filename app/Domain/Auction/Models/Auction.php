<?php

declare(strict_types=1);

namespace App\Domain\Auction\Models;

use App\Domain\Auction\Events\AuctionCreated;
use App\Domain\Auction\Events\AuctionActivated;
use App\Domain\Auction\Events\AuctionEnded;
use App\Domain\Auction\Events\AuctionWon;
use App\Domain\Auction\ValueObjects\AuctionDuration;
use App\Domain\Auction\ValueObjects\AuctionStatus;
use App\Domain\Auction\ValueObjects\BidAmount;
use App\Domain\Auction\ValueObjects\ReservePrice;
use App\Domain\Shared\Models\AggregateRoot;
use App\Domain\Shared\ValueObjects\Id;
use App\Domain\Shared\ValueObjects\Money;
use App\Domain\Shared\ValueObjects\Timestamp;
use App\Domain\User\ValueObjects\UserId;
use InvalidArgumentException;

class Auction extends AggregateRoot
{
    private Id $id;
    private UserId $sellerId;
    private Id $categoryId;
    private string $title;
    private string $description;
    private ?string $condition;
    private Money $startingPrice;
    private ReservePrice $reservePrice;
    private BidAmount $currentBid;
    private Money $bidIncrement;
    private ?Money $buyoutPrice;
    private AuctionDuration $duration;
    private ?Timestamp $actualEndTime;
    private AuctionStatus $status;
    private bool $autoExtend;
    private int $extendMinutes;
    private int $viewsCount;
    private int $watchersCount;
    private int $bidsCount;
    private ?UserId $winnerId;
    private ?Money $finalPrice;
    private bool $featured;
    private ?Timestamp $featuredUntil;
    private Timestamp $createdAt;
    private Timestamp $updatedAt;

    public function __construct(
        Id $id,
        UserId $sellerId,
        Id $categoryId,
        string $title,
        string $description,
        Money $startingPrice,
        AuctionDuration $duration,
        ReservePrice $reservePrice = null,
        ?Money $buyoutPrice = null
    ) {
        $this->validateCreation($title, $description, $startingPrice, $reservePrice);

        $this->id = $id;
        $this->sellerId = $sellerId;
        $this->categoryId = $categoryId;
        $this->title = $title;
        $this->description = $description;
        $this->condition = null;
        $this->startingPrice = $startingPrice;
        $this->reservePrice = $reservePrice ?? ReservePrice::none();
        $this->currentBid = BidAmount::fromFloat(0.0, $startingPrice->currency());
        $this->bidIncrement = new Money(1.00, $startingPrice->currency());
        $this->buyoutPrice = $buyoutPrice;
        $this->duration = $duration;
        $this->actualEndTime = null;
        $this->status = AuctionStatus::draft();
        $this->autoExtend = false;
        $this->extendMinutes = 10;
        $this->viewsCount = 0;
        $this->watchersCount = 0;
        $this->bidsCount = 0;
        $this->winnerId = null;
        $this->finalPrice = null;
        $this->featured = false;
        $this->featuredUntil = null;
        $this->createdAt = Timestamp::now();
        $this->updatedAt = Timestamp::now();

        $this->recordEvent(new AuctionCreated($this->sellerId, [
            'auction_id' => $this->id->value(),
            'title' => $this->title,
            'starting_price' => $this->startingPrice->amount(),
            'currency' => $this->startingPrice->currency(),
            'start_time' => $this->duration->startTime()->toISOString(),
            'end_time' => $this->duration->endTime()->toISOString(),
        ]));
    }

    // Getters
    public function id(): Id
    {
        return $this->id;
    }

    public function sellerId(): UserId
    {
        return $this->sellerId;
    }

    public function categoryId(): Id
    {
        return $this->categoryId;
    }

    public function title(): string
    {
        return $this->title;
    }

    public function description(): string
    {
        return $this->description;
    }

    public function condition(): ?string
    {
        return $this->condition;
    }

    public function startingPrice(): Money
    {
        return $this->startingPrice;
    }

    public function reservePrice(): ReservePrice
    {
        return $this->reservePrice;
    }

    public function currentBid(): BidAmount
    {
        return $this->currentBid;
    }

    public function bidIncrement(): Money
    {
        return $this->bidIncrement;
    }

    public function buyoutPrice(): ?Money
    {
        return $this->buyoutPrice;
    }

    public function duration(): AuctionDuration
    {
        return $this->duration;
    }

    public function status(): AuctionStatus
    {
        return $this->status;
    }

    public function winnerId(): ?UserId
    {
        return $this->winnerId;
    }

    public function finalPrice(): ?Money
    {
        return $this->finalPrice;
    }

    public function bidsCount(): int
    {
        return $this->bidsCount;
    }

    public function viewsCount(): int
    {
        return $this->viewsCount;
    }

    public function watchersCount(): int
    {
        return $this->watchersCount;
    }

    public function isFeatured(): bool
    {
        return $this->featured &&
               ($this->featuredUntil === null || Timestamp::now()->isBefore($this->featuredUntil));
    }

    public function createdAt(): Timestamp
    {
        return $this->createdAt;
    }

    public function updatedAt(): Timestamp
    {
        return $this->updatedAt;
    }

    public function actualEndTime(): ?Timestamp
    {
        return $this->actualEndTime;
    }

    public function autoExtend(): bool
    {
        return $this->autoExtend;
    }

    public function extendMinutes(): int
    {
        return $this->extendMinutes;
    }

    public function featured(): bool
    {
        return $this->featured;
    }

    public function featuredUntil(): ?Timestamp
    {
        return $this->featuredUntil;
    }

    // Business Logic Methods
    public function activate(): void
    {
        if (!$this->status->isDraft() && !$this->status->isScheduled()) {
            throw new InvalidArgumentException('Only draft or scheduled auctions can be activated');
        }

        if ($this->duration->hasEnded()) {
            throw new InvalidArgumentException('Cannot activate an auction that has already ended');
        }

        $this->status = AuctionStatus::active();
        $this->updatedAt = Timestamp::now();

        $this->recordEvent(new AuctionActivated($this->sellerId, [
            'auction_id' => $this->id->value(),
            'title' => $this->title,
        ]));
    }

    public function end(?UserId $winnerId = null, ?Money $finalPrice = null): void
    {
        if (!$this->status->isActive()) {
            throw new InvalidArgumentException('Only active auctions can be ended');
        }

        $this->status = AuctionStatus::ended();
        $this->actualEndTime = Timestamp::now();
        $this->winnerId = $winnerId;
        $this->finalPrice = $finalPrice;
        $this->updatedAt = Timestamp::now();

        $this->recordEvent(new AuctionEnded($this->sellerId, [
            'auction_id' => $this->id->value(),
            'title' => $this->title,
            'winner_id' => $winnerId?->value(),
            'final_price' => $finalPrice?->amount(),
            'currency' => $finalPrice?->currency(),
            'bids_count' => $this->bidsCount,
        ]));

        if ($winnerId && $finalPrice) {
            $this->recordEvent(new AuctionWon($winnerId, [
                'auction_id' => $this->id->value(),
                'title' => $this->title,
                'final_price' => $finalPrice->amount(),
                'currency' => $finalPrice->currency(),
                'seller_id' => $this->sellerId->value(),
            ]));
        }
    }

    public function cancel(): void
    {
        if (!$this->status->canCancel()) {
            throw new InvalidArgumentException('This auction cannot be cancelled');
        }

        $this->status = AuctionStatus::cancelled();
        $this->updatedAt = Timestamp::now();
    }

    public function incrementViews(): void
    {
        $this->viewsCount++;
        $this->updatedAt = Timestamp::now();
    }

    public function incrementWatchers(): void
    {
        $this->watchersCount++;
        $this->updatedAt = Timestamp::now();
    }

    public function decrementWatchers(): void
    {
        if ($this->watchersCount > 0) {
            $this->watchersCount--;
            $this->updatedAt = Timestamp::now();
        }
    }

    private function validateCreation(
        string $title,
        string $description,
        Money $startingPrice,
        ?ReservePrice $reservePrice
    ): void {
        if (empty(trim($title))) {
            throw new InvalidArgumentException('Auction title cannot be empty');
        }

        if (strlen($title) > 500) {
            throw new InvalidArgumentException('Auction title cannot exceed 500 characters');
        }

        if (empty(trim($description))) {
            throw new InvalidArgumentException('Auction description cannot be empty');
        }

        if ($startingPrice->isNegative() || $startingPrice->isZero()) {
            throw new InvalidArgumentException('Starting price must be positive');
        }

        if ($reservePrice && !$reservePrice->isValidForStartingPrice($startingPrice)) {
            throw new InvalidArgumentException('Reserve price must be greater than or equal to starting price');
        }
    }
}

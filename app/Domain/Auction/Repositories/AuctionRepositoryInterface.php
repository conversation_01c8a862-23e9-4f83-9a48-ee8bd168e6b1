<?php

declare(strict_types=1);

namespace App\Domain\Auction\Repositories;

use App\Domain\Auction\Models\Auction;
use App\Models\Auction as EloquentAuction;
use App\Domain\Auction\ValueObjects\AuctionStatus;
use App\Domain\Shared\ValueObjects\Id;
use App\Domain\User\ValueObjects\UserId;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

interface AuctionRepositoryInterface
{
    /**
     * Find auction by ID
     */
    public function findById(Id $id): ?EloquentAuction;

    /**
     * Find auction by ID or throw exception
     */
    public function findByIdOrFail(Id $id): EloquentAuction;

    /**
     * Save auction
     */
    public function save(EloquentAuction $auction): void;

    /**
     * Delete auction
     */
    public function delete(EloquentAuction $auction): void;

    /**
     * Find auctions by seller
     */
    public function findBySeller(UserId $sellerId, int $perPage = 15): LengthAwarePaginator;

    /**
     * Find auctions by category
     */
    public function findByCategory(Id $categoryId, int $perPage = 15): LengthAwarePaginator;

    /**
     * Find auctions by status
     */
    public function findByStatus(AuctionStatus $status, int $perPage = 15): LengthAwarePaginator;

    /**
     * Find active auctions
     */
    public function findActive(int $perPage = 15): LengthAwarePaginator;

    /**
     * Find ending soon auctions (within specified hours)
     */
    public function findEndingSoon(int $hours = 24, int $perPage = 15): LengthAwarePaginator;

    /**
     * Find featured auctions
     */
    public function findFeatured(int $perPage = 15): LengthAwarePaginator;

    /**
     * Find auctions that should end (past end time but still active)
     */
    public function findExpiredActive(): Collection;

    /**
     * Find auctions that should be activated (scheduled and start time has passed)
     */
    public function findScheduledToActivate(): Collection;

    /**
     * Search auctions by title and description
     */
    public function search(string $query, array $filters = [], int $perPage = 15): LengthAwarePaginator;

    /**
     * Find auctions watched by user
     */
    public function findWatchedByUser(UserId $userId, int $perPage = 15): LengthAwarePaginator;

    /**
     * Find auctions watched by user (alias for findWatchedByUser)
     */
    public function findUserWatched(UserId $userId, int $perPage = 15): LengthAwarePaginator;

    /**
     * Find auctions won by user
     */
    public function findWonByUser(UserId $userId, int $perPage = 15): LengthAwarePaginator;

    /**
     * Find auctions bid on by user
     */
    public function findBidOnByUser(UserId $userId, int $perPage = 15): LengthAwarePaginator;

    /**
     * Get auction statistics
     */
    public function getStatistics(): array;

    /**
     * Count auctions by status
     */
    public function countByStatus(AuctionStatus $status): int;

    /**
     * Find similar auctions (by category and price range)
     */
    public function findSimilar(EloquentAuction $auction, int $limit = 10): Collection;

    /**
     * Find related auctions (similar to findSimilar but with different criteria)
     */
    public function findRelated(EloquentAuction $auction, int $limit = 10): Collection;

    /**
     * Find recently viewed auctions by user
     */
    public function findRecentlyViewedByUser(UserId $userId, int $limit = 10): Collection;

    /**
     * Increment view count
     */
    public function incrementViewCount(Id $auctionId): void;

    /**
     * Update watchers count
     */
    public function updateWatchersCount(Id $auctionId, int $count): void;

    /**
     * Update bids count
     */
    public function updateBidsCount(Id $auctionId, int $count): void;

    /**
     * Count user's active auctions
     */
    public function countUserActiveAuctions(UserId $userId): int;

    /**
     * Count user's total auctions
     */
    public function countUserAuctions(UserId $userId): int;

    /**
     * Count user's won auctions
     */
    public function countUserWonAuctions(UserId $userId): int;

    /**
     * Find user's recent auctions
     */
    public function findUserRecent(UserId $userId, int $limit = 10): Collection;
}

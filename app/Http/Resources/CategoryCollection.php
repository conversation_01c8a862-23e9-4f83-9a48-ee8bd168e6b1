<?php

declare(strict_types=1);

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\ResourceCollection;

class CategoryCollection extends ResourceCollection
{
    /**
     * The resource that this resource collects.
     *
     * @var string
     */
    public $collects = CategoryResource::class;

    /**
     * Transform the resource collection into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection,
            'meta' => [
                'total' => $this->collection->count(),
                'has_hierarchy' => $this->hasHierarchy(),
                'max_depth' => $this->getMaxDepth(),
                'root_categories_count' => $this->getRootCategoriesCount(),
            ],
        ];
    }

    /**
     * Get additional data that should be returned with the resource array.
     *
     * @return array<string, mixed>
     */
    public function with(Request $request): array
    {
        return [
            'links' => [
                'self' => $request->url(),
                'categories_api' => route('api.categories.index'),
            ],
            'meta' => [
                'version' => '1.0',
                'generated_at' => now()->toISOString(),
                'resource_type' => 'CategoryCollection',
            ],
        ];
    }

    /**
     * Check if the collection contains hierarchical data
     */
    private function hasHierarchy(): bool
    {
        return $this->collection->contains(function ($category) {
            return $category->children && $category->children->isNotEmpty();
        });
    }

    /**
     * Get the maximum depth of the category hierarchy
     */
    private function getMaxDepth(): int
    {
        $maxDepth = 0;
        
        foreach ($this->collection as $category) {
            $depth = $this->calculateDepth($category, 1);
            $maxDepth = max($maxDepth, $depth);
        }
        
        return $maxDepth;
    }

    /**
     * Calculate the depth of a category recursively
     */
    private function calculateDepth($category, int $currentDepth): int
    {
        if (!$category->children || $category->children->isEmpty()) {
            return $currentDepth;
        }
        
        $maxChildDepth = $currentDepth;
        foreach ($category->children as $child) {
            $childDepth = $this->calculateDepth($child, $currentDepth + 1);
            $maxChildDepth = max($maxChildDepth, $childDepth);
        }
        
        return $maxChildDepth;
    }

    /**
     * Get the count of root categories (categories without parents)
     */
    private function getRootCategoriesCount(): int
    {
        return $this->collection->filter(function ($category) {
            return is_null($category->parent_id);
        })->count();
    }

    /**
     * Customize the outgoing response for the resource.
     */
    public function withResponse(Request $request, $response): void
    {
        $response->header('X-Resource-Type', 'CategoryCollection');
        $response->header('X-Total-Count', (string) $this->collection->count());
    }
}

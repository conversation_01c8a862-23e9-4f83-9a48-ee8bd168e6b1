<?php

declare(strict_types=1);

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Auction;
use App\Models\Bid;
use App\Models\Payment;
use App\Models\Setting;
use App\Models\User;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class DashboardController extends Controller
{
    /**
     * Display the admin dashboard
     */
    public function index(): Response
    {
        $stats = [
            'users' => [
                'total' => User::count(),
                'active' => User::where('is_active', true)->count(),
                'verified' => User::whereNotNull('email_verified_at')->count(),
                'new_this_month' => User::where('created_at', '>=', now()->startOfMonth())->count(),
            ],
            'auctions' => [
                'total' => Auction::count(),
                'active' => Auction::where('status', 'active')->count(),
                'draft' => Auction::where('status', 'draft')->count(),
                'ended' => Auction::where('status', 'ended')->count(),
                'new_today' => Auction::where('created_at', '>=', now()->startOfDay())->count(),
            ],
            'bids' => [
                'total' => Bid::count(),
                'today' => Bid::where('created_at', '>=', now()->startOfDay())->count(),
                'this_week' => Bid::where('created_at', '>=', now()->startOfWeek())->count(),
                'total_value' => Bid::sum('amount'),
            ],
            'payments' => [
                'total' => Payment::count(),
                'completed' => Payment::where('status', 'completed')->count(),
                'pending' => Payment::where('status', 'pending')->count(),
                'failed' => Payment::where('status', 'failed')->count(),
                'total_amount' => Payment::where('status', 'completed')->sum('amount'),
            ],
        ];

        $recentAuctions = Auction::with(['user', 'category'])
            ->latest()
            ->limit(10)
            ->get();

        $recentUsers = User::latest()
            ->limit(10)
            ->get();

        $systemSettings = [
            'allow_user_auction_creation' => Setting::get('allow_user_auction_creation', true),
            'maintenance_mode' => Setting::get('maintenance_mode', false),
            'require_email_verification_for_bidding' => Setting::get('require_email_verification_for_bidding', true),
        ];

        return Inertia::render('Admin/Dashboard', [
            'stats' => $stats,
            'recentAuctions' => $recentAuctions,
            'recentUsers' => $recentUsers,
            'systemSettings' => $systemSettings,
        ]);
    }
}

<?php

declare(strict_types=1);

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Setting;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class SettingsController extends Controller
{
    /**
     * Display the settings management page
     */
    public function index(): Response
    {
        $settings = Setting::orderBy('key')->get()->groupBy(function ($setting) {
            // Group settings by category based on key prefix
            $parts = explode('_', $setting->key);
            return $parts[0] ?? 'general';
        });

        return Inertia::render('Admin/Settings/Index', [
            'settings' => $settings,
            'categories' => [
                'allow' => 'Permissions',
                'site' => 'Site Configuration',
                'max' => 'Limits',
                'min' => 'Minimums',
                'require' => 'Requirements',
                'enable' => 'Features',
                'default' => 'Defaults',
                'maintenance' => 'Maintenance',
                'general' => 'General',
            ],
        ]);
    }

    /**
     * Show a specific setting
     */
    public function show(string $key): Response
    {
        $setting = Setting::where('key', $key)->firstOrFail();

        return Inertia::render('Admin/Settings/Show', [
            'setting' => $setting,
        ]);
    }

    /**
     * Update settings
     */
    public function update(Request $request): RedirectResponse
    {
        $request->validate([
            'settings' => 'required|array',
            'settings.*.key' => 'required|string',
            'settings.*.value' => 'required',
            'settings.*.type' => 'required|string|in:string,boolean,integer,float,json',
        ]);

        foreach ($request->settings as $settingData) {
            $setting = Setting::where('key', $settingData['key'])->first();
            
            if ($setting) {
                $setting->update([
                    'value' => Setting::prepareValue($settingData['value'], $settingData['type']),
                    'type' => $settingData['type'],
                ]);
            }
        }

        // Clear settings cache
        Setting::clearCache();

        return redirect()
            ->route('admin.settings.index')
            ->with('success', 'Settings updated successfully!');
    }
}

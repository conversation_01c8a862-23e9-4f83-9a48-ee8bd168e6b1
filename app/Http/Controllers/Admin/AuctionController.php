<?php

declare(strict_types=1);

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Auction;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class AuctionController extends Controller
{
    /**
     * Display a listing of auctions
     */
    public function index(Request $request): Response
    {
        $query = Auction::query();

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                  ->orWhere('description', 'like', "%{$search}%");
            });
        }

        if ($request->filled('status')) {
            $query->where('status', $request->status);
        }

        $auctions = $query->with(['user', 'category', 'bids'])
            ->withCount(['bids'])
            ->latest()
            ->paginate(20)
            ->withQueryString();

        return Inertia::render('Admin/Auctions/Index', [
            'auctions' => $auctions,
            'filters' => $request->only(['search', 'status']),
        ]);
    }

    /**
     * Display the specified auction
     */
    public function show(Auction $auction): Response
    {
        $auction->load(['user', 'category', 'bids.user', 'images']);

        return Inertia::render('Admin/Auctions/Show', [
            'auction' => $auction,
        ]);
    }

    /**
     * Update the specified auction
     */
    public function update(Request $request, Auction $auction): RedirectResponse
    {
        $request->validate([
            'title' => 'required|string|max:500',
            'description' => 'required|string|max:10000',
            'status' => 'required|string|in:draft,active,ended,cancelled',
            'is_featured' => 'boolean',
        ]);

        $auction->update($request->only(['title', 'description', 'status', 'is_featured']));

        return redirect()
            ->route('admin.auctions.show', $auction)
            ->with('success', 'Auction updated successfully!');
    }

    /**
     * Remove the specified auction
     */
    public function destroy(Auction $auction): RedirectResponse
    {
        $auction->delete();

        return redirect()
            ->route('admin.auctions.index')
            ->with('success', 'Auction deleted successfully!');
    }

    /**
     * Feature an auction
     */
    public function feature(Auction $auction): RedirectResponse
    {
        $auction->update([
            'is_featured' => true,
            'featured_until' => now()->addDays(7),
        ]);

        return redirect()
            ->back()
            ->with('success', 'Auction featured successfully!');
    }

    /**
     * Unfeature an auction
     */
    public function unfeature(Auction $auction): RedirectResponse
    {
        $auction->update([
            'is_featured' => false,
            'featured_until' => null,
        ]);

        return redirect()
            ->back()
            ->with('success', 'Auction unfeatured successfully!');
    }

    /**
     * Approve an auction
     */
    public function approve(Auction $auction): RedirectResponse
    {
        $auction->update(['status' => 'active']);

        return redirect()
            ->back()
            ->with('success', 'Auction approved and activated!');
    }

    /**
     * Reject an auction
     */
    public function reject(Auction $auction): RedirectResponse
    {
        $auction->update(['status' => 'cancelled']);

        return redirect()
            ->back()
            ->with('success', 'Auction rejected and cancelled!');
    }
}

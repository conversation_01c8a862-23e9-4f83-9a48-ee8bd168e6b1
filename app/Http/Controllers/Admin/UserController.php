<?php

declare(strict_types=1);

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class UserController extends Controller
{
    /**
     * Display a listing of users
     */
    public function index(Request $request): Response
    {
        $query = User::query();

        if ($request->filled('search')) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        if ($request->filled('status')) {
            $query->where('is_active', $request->status === 'active');
        }

        $users = $query->with(['auctions', 'bids'])
            ->withCount(['auctions', 'bids'])
            ->latest()
            ->paginate(20)
            ->withQueryString();

        return Inertia::render('Admin/Users/<USER>', [
            'users' => $users,
            'filters' => $request->only(['search', 'status']),
        ]);
    }

    /**
     * Display the specified user
     */
    public function show(User $user): Response
    {
        $user->load(['auctions', 'bids', 'payments']);

        return Inertia::render('Admin/Users/<USER>', [
            'user' => $user,
        ]);
    }

    /**
     * Update the specified user
     */
    public function update(Request $request, User $user): RedirectResponse
    {
        $request->validate([
            'name' => 'required|string|max:255',
            'email' => 'required|string|email|max:255|unique:users,email,' . $user->id,
            'is_active' => 'boolean',
            'role' => 'nullable|string|in:user,admin,moderator',
        ]);

        $user->update($request->only(['name', 'email', 'is_active', 'role']));

        return redirect()
            ->route('admin.users.show', $user)
            ->with('success', 'User updated successfully!');
    }

    /**
     * Remove the specified user
     */
    public function destroy(User $user): RedirectResponse
    {
        // Prevent deleting the current admin user
        if ($user->id === auth()->id()) {
            return redirect()
                ->route('admin.users.index')
                ->with('error', 'You cannot delete your own account.');
        }

        $user->delete();

        return redirect()
            ->route('admin.users.index')
            ->with('success', 'User deleted successfully!');
    }

    /**
     * Activate a user
     */
    public function activate(User $user): RedirectResponse
    {
        $user->update(['is_active' => true]);

        return redirect()
            ->back()
            ->with('success', 'User activated successfully!');
    }

    /**
     * Deactivate a user
     */
    public function deactivate(User $user): RedirectResponse
    {
        // Prevent deactivating the current admin user
        if ($user->id === auth()->id()) {
            return redirect()
                ->back()
                ->with('error', 'You cannot deactivate your own account.');
        }

        $user->update(['is_active' => false]);

        return redirect()
            ->back()
            ->with('success', 'User deactivated successfully!');
    }
}

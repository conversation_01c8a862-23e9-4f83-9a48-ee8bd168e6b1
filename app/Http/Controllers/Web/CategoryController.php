<?php

declare(strict_types=1);

namespace App\Http\Controllers\Web;

use App\Domain\Auction\Repositories\AuctionRepositoryInterface;
use App\Domain\Auction\Repositories\CategoryRepositoryInterface;
use App\Domain\Shared\ValueObjects\Id;
use App\Domain\Shared\ValueObjects\Slug;
use App\Http\Controllers\Controller;
use App\Http\Resources\AuctionResource;
use App\Http\Resources\CategoryResource;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class CategoryController extends Controller
{
    public function __construct(
        private CategoryRepositoryInterface $categoryRepository,
        private AuctionRepositoryInterface $auctionRepository
    ) {}

    /**
     * Display a listing of categories
     */
    public function index(): Response
    {
        $categories = $this->categoryRepository->findAllWithHierarchy();
        $featuredCategories = $this->categoryRepository->findFeatured();
        $popularCategories = $this->categoryRepository->findPopular(6);

        return Inertia::render('Categories/Index', [
            'categories' => CategoryResource::collection($categories),
            'featured_categories' => CategoryResource::collection($featuredCategories),
            'popular_categories' => CategoryResource::collection($popularCategories),
        ]);
    }

    /**
     * Display the specified category and its auctions
     */
    public function show(string $slug, Request $request): Response
    {
        $category = $this->categoryRepository->findBySlug(Slug::fromString($slug));

        if (!$category) {
            abort(404, 'Category not found');
        }

        $perPage = min($request->get('per_page', 15), 50);
        $status = $request->get('status');
        $search = $request->get('search');
        $sortBy = $request->get('sort_by', 'ending_soon');

        $filters = [
            'status' => $status,
            'search' => $search,
            'sort_by' => $sortBy,
        ];

        $auctions = $this->auctionRepository->findByCategory(
            Id::fromString((string) $category->id),
            $perPage
        );

        // Get subcategories
        $subcategories = $this->categoryRepository->findChildCategories(Id::fromString((string) $category->id));

        return Inertia::render('Categories/Show', [
            'category' => new CategoryResource($category),
            'auctions' => AuctionResource::collection($auctions),
            'subcategories' => CategoryResource::collection($subcategories),
            'filters' => array_merge($filters, ['per_page' => $perPage]),
        ]);
    }

    /**
     * Display auctions for a category
     */
    public function auctions(string $slug, Request $request): Response
    {
        $category = $this->categoryRepository->findBySlug(Slug::fromString($slug));

        if (!$category) {
            abort(404, 'Category not found');
        }

        $perPage = min($request->get('per_page', 15), 50);
        $status = $request->get('status');
        $search = $request->get('search');
        $sortBy = $request->get('sort_by', 'ending_soon');

        $filters = [
            'status' => $status,
            'search' => $search,
            'sort_by' => $sortBy,
        ];

        $auctions = $this->auctionRepository->findByCategory(
            Id::fromString((string) $category->id),
            $perPage
        );

        return Inertia::render('Categories/Auctions', [
            'category' => new CategoryResource($category),
            'auctions' => AuctionResource::collection($auctions),
            'filters' => array_merge($filters, ['per_page' => $perPage]),
        ]);
    }
}

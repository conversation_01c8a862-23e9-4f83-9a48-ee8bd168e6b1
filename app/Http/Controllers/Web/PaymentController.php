<?php

declare(strict_types=1);

namespace App\Http\Controllers\Web;

use App\Actions\Payment\ProcessAuctionPaymentAction;
use App\Actions\Payment\RefundPaymentAction;
use App\Domain\Auction\Repositories\AuctionRepositoryInterface;
use App\Domain\Payment\Repositories\PaymentRepositoryInterface;
use App\Domain\Shared\ValueObjects\Id;
use App\Domain\User\ValueObjects\UserId;
use App\Http\Controllers\Controller;
use App\Http\Requests\ProcessPaymentRequest;
use App\Http\Resources\AuctionResource;
use App\Http\Resources\PaymentResource;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class PaymentController extends Controller
{
    public function __construct(
        private PaymentRepositoryInterface $paymentRepository,
        private AuctionRepositoryInterface $auctionRepository
    ) {}

    /**
     * Display user's payments
     */
    public function index(Request $request): Response
    {
        $perPage = min($request->get('per_page', 15), 50);
        $status = $request->get('status');
        $userId = UserId::fromString((string) auth()->id());

        if ($status) {
            $payments = $this->paymentRepository->findUserByStatus($userId, $status, $perPage);
        } else {
            $payments = $this->paymentRepository->findUserPayments($userId, $perPage);
        }

        return Inertia::render('Payments/Index', [
            'payments' => PaymentResource::collection($payments),
            'filters' => [
                'status' => $status,
                'per_page' => $perPage,
            ],
        ]);
    }

    /**
     * Show payment form for an auction
     */
    public function create(int $auctionId): Response
    {
        $auction = $this->auctionRepository->findByIdOrFail(Id::fromString((string) $auctionId));
        
        // Check if user won this auction
        $userId = UserId::fromString((string) auth()->id());
        if (!$auction->isWonBy($userId)) {
            abort(403, 'You did not win this auction');
        }

        // Check if already paid
        $existingPayment = $this->paymentRepository->findByAuction(Id::fromString((string) $auctionId));
        if ($existingPayment && $existingPayment->isSuccessful()) {
            return redirect()
                ->route('payments.show', $existingPayment->id()->value())
                ->with('info', 'This auction has already been paid for.');
        }

        return Inertia::render('Payments/Create', [
            'auction' => new AuctionResource($auction),
            'stripe_public_key' => config('services.stripe.key'),
        ]);
    }

    /**
     * Process payment for an auction
     */
    public function store(ProcessPaymentRequest $request, ProcessAuctionPaymentAction $action): RedirectResponse
    {
        try {
            $data = $request->validated();
            $data['user_id'] = auth()->id();

            $payment = $action->execute($data);

            return redirect()
                ->route('payments.show', $payment->id()->value())
                ->with('success', 'Payment processed successfully!');
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->withInput()
                ->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Display the specified payment
     */
    public function show(int $id): Response
    {
        $payment = $this->paymentRepository->findByIdOrFail(Id::fromString((string) $id));
        
        // Check permissions
        $userId = UserId::fromString((string) auth()->id());
        if (!$payment->userId()->equals($userId)) {
            abort(403, 'Unauthorized to view this payment');
        }

        $auction = $this->auctionRepository->findByIdOrFail($payment->auctionId());

        return Inertia::render('Payments/Show', [
            'payment' => new PaymentResource($payment),
            'auction' => new AuctionResource($auction),
        ]);
    }

    /**
     * Create payment intent for Stripe (AJAX)
     */
    public function createIntent(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'auction_id' => 'required|integer|exists:auctions,id',
                'amount' => 'required|numeric|min:0.01',
                'currency' => 'nullable|string|in:USD,EUR,GBP,CAD',
            ]);

            $auction = $this->auctionRepository->findByIdOrFail(
                Id::fromString((string) $request->auction_id)
            );

            // Check if user won this auction
            $userId = UserId::fromString((string) auth()->id());
            if (!$auction->isWonBy($userId)) {
                return response()->json([
                    'message' => 'You did not win this auction',
                ], 403);
            }

            // Create Stripe payment intent
            $stripe = new \Stripe\StripeClient(config('services.stripe.secret'));
            
            $intent = $stripe->paymentIntents->create([
                'amount' => (int) ($request->amount * 100), // Convert to cents
                'currency' => $request->currency ?? 'usd',
                'metadata' => [
                    'auction_id' => $request->auction_id,
                    'user_id' => auth()->id(),
                ],
            ]);

            return response()->json([
                'data' => [
                    'client_secret' => $intent->client_secret,
                    'payment_intent_id' => $intent->id,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to create payment intent',
                'error' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Confirm payment intent (AJAX)
     */
    public function confirmIntent(Request $request): JsonResponse
    {
        try {
            $request->validate([
                'payment_intent_id' => 'required|string',
                'payment_method_id' => 'required|string',
            ]);

            // Retrieve and confirm the payment intent
            $stripe = new \Stripe\StripeClient(config('services.stripe.secret'));
            
            $intent = $stripe->paymentIntents->confirm(
                $request->payment_intent_id,
                ['payment_method' => $request->payment_method_id]
            );

            return response()->json([
                'data' => [
                    'status' => $intent->status,
                    'payment_intent' => $intent,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to confirm payment',
                'error' => $e->getMessage(),
            ], 400);
        }
    }

    /**
     * Handle Stripe webhook
     */
    public function webhook(Request $request): JsonResponse
    {
        try {
            $payload = $request->getContent();
            $sig_header = $request->header('Stripe-Signature');
            $endpoint_secret = config('services.stripe.webhook_secret');

            $event = \Stripe\Webhook::constructEvent($payload, $sig_header, $endpoint_secret);

            // Handle the event
            switch ($event['type']) {
                case 'payment_intent.succeeded':
                    $paymentIntent = $event['data']['object'];
                    // Handle successful payment
                    $this->handleSuccessfulPayment($paymentIntent);
                    break;
                case 'payment_intent.payment_failed':
                    $paymentIntent = $event['data']['object'];
                    // Handle failed payment
                    $this->handleFailedPayment($paymentIntent);
                    break;
                default:
                    // Unexpected event type
                    break;
            }

            return response()->json(['status' => 'success']);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Webhook error',
                'error' => $e->getMessage(),
            ], 400);
        }
    }

    private function handleSuccessfulPayment($paymentIntent): void
    {
        // Implementation for handling successful payment
        // This would update the payment status in the database
    }

    private function handleFailedPayment($paymentIntent): void
    {
        // Implementation for handling failed payment
        // This would update the payment status in the database
    }
}

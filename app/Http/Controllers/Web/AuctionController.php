<?php

declare(strict_types=1);

namespace App\Http\Controllers\Web;

use App\Actions\Auction\ActivateAuctionAction;
use App\Actions\Auction\CreateAuctionAction;
use App\Actions\Auction\EndAuctionAction;
use App\Actions\Auction\UpdateAuctionAction;
use App\Actions\Auction\UploadAuctionImagesAction;
use App\Domain\Auction\Repositories\AuctionRepositoryInterface;
use App\Domain\Auction\Repositories\CategoryRepositoryInterface;
use App\Domain\Shared\ValueObjects\Id;
use App\Domain\User\ValueObjects\UserId;
use App\Http\Controllers\Controller;
use App\Http\Requests\CreateAuctionRequest;
use App\Http\Requests\UpdateAuctionRequest;
use App\Http\Requests\UploadAuctionImagesRequest;
use App\Http\Resources\AuctionResource;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Inertia\Inertia;
use Inertia\Response;

class AuctionController extends Controller
{
    public function __construct(
        private AuctionRepositoryInterface $auctionRepository,
        private CategoryRepositoryInterface $categoryRepository
    ) {}

    /**
     * Display a listing of auctions
     */
    public function index(Request $request): Response
    {
        $perPage = min($request->get('per_page', 15), 50);
        $status = $request->get('status');
        $category = $request->get('category');
        $search = $request->get('search');

        if ($search) {
            $auctions = $this->auctionRepository->search($search, [
                'status' => $status,
                'category_id' => $category,
            ], $perPage);
        } elseif ($status) {
            $auctions = $this->auctionRepository->findByStatus(
                new \App\Domain\Auction\ValueObjects\AuctionStatus($status),
                $perPage
            );
        } elseif ($category) {
            $auctions = $this->auctionRepository->findByCategory(
                Id::fromString($category),
                $perPage
            );
        } else {
            $auctions = $this->auctionRepository->findActive($perPage);
        }

        $categories = $this->categoryRepository->findAll();

        return Inertia::render('Auctions/Index', [
            'auctions' => AuctionResource::collection($auctions),
            'categories' => $categories,
            'filters' => [
                'search' => $search,
                'status' => $status,
                'category' => $category,
                'per_page' => $perPage,
            ],
        ]);
    }

    /**
     * Display featured auctions
     */
    public function featured(Request $request): Response
    {
        $perPage = min($request->get('per_page', 15), 50);
        $auctions = $this->auctionRepository->findFeatured($perPage);

        return Inertia::render('Auctions/Featured', [
            'auctions' => AuctionResource::collection($auctions),
        ]);
    }

    /**
     * Display ending soon auctions
     */
    public function endingSoon(Request $request): Response
    {
        $perPage = min($request->get('per_page', 15), 50);
        $auctions = $this->auctionRepository->findEndingSoon($perPage);

        return Inertia::render('Auctions/EndingSoon', [
            'auctions' => AuctionResource::collection($auctions),
        ]);
    }

    /**
     * Display the specified auction
     */
    public function show(int $id): Response
    {
        $auction = $this->auctionRepository->findByIdOrFail(Id::fromString((string) $id));

        // Increment view count
        $this->auctionRepository->incrementViewCount(Id::fromString((string) $id));

        // Get related auctions
        $relatedAuctions = $this->auctionRepository->findRelated($auction, 4);

        return Inertia::render('Auctions/Show', [
            'auction' => new AuctionResource($auction),
            'relatedAuctions' => AuctionResource::collection($relatedAuctions),
        ]);
    }

    /**
     * Show the form for creating a new auction
     */
    public function create(): Response
    {
        // Check if user can create auctions
        if (!auth()->user()->canCreateAuctions()) {
            abort(403, 'You are not authorized to create auctions.');
        }

        $categories = $this->categoryRepository->findAll();

        return Inertia::render('Auctions/Create', [
            'categories' => $categories,
        ]);
    }

    /**
     * Store a newly created auction
     */
    public function store(CreateAuctionRequest $request, CreateAuctionAction $action): RedirectResponse
    {
        try {
            $data = $request->validated();
            $data['user_id'] = auth()->id();

            $auction = $action->execute($data);

            return redirect()
                ->route('auctions.show', $auction->id()->value())
                ->with('success', 'Auction created successfully!');
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->withInput()
                ->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Show the form for editing the specified auction
     */
    public function edit(int $id): Response
    {
        $auction = $this->auctionRepository->findById(Id::fromString((string) $id));

        if (!$auction) {
            abort(404, 'Auction not found');
        }

        // Check permissions
        if ($auction->user_id !== auth()->id()) {
            abort(404, 'Auction not found'); // Return 404 instead of 403 for security
        }

        $categories = $this->categoryRepository->findAll();

        return Inertia::render('Auctions/Edit', [
            'auction' => new AuctionResource($auction),
            'categories' => $categories,
        ]);
    }

    /**
     * Update the specified auction
     */
    public function update(
        int $id,
        UpdateAuctionRequest $request,
        UpdateAuctionAction $action
    ): RedirectResponse {
        try {
            $userId = UserId::fromString((string) auth()->id());
            $auction = $action->execute($id, $request->validated(), $userId);

            return redirect()
                ->route('auctions.show', $auction->id()->value())
                ->with('success', 'Auction updated successfully!');
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->withInput()
                ->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Remove the specified auction
     */
    public function destroy(int $id): RedirectResponse
    {
        try {
            $auction = $this->auctionRepository->findByIdOrFail(Id::fromString((string) $id));

            // Check permissions
            if (!$auction->sellerId()->equals(UserId::fromString((string) auth()->id()))) {
                abort(403, 'Unauthorized to delete this auction');
            }

            $this->auctionRepository->delete($auction);

            return redirect()
                ->route('auctions.index')
                ->with('success', 'Auction deleted successfully!');
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Activate an auction
     */
    public function activate(int $id, ActivateAuctionAction $action): RedirectResponse
    {
        try {
            // First check if auction exists and user owns it
            $auction = $this->auctionRepository->findById(Id::fromString((string) $id));
            if (!$auction || $auction->user_id !== auth()->id()) {
                return redirect()
                    ->back()
                    ->withErrors(['error' => 'Auction not found']);
            }

            $userId = UserId::fromString((string) auth()->id());
            $auction = $action->execute($id, $userId);

            return redirect()
                ->route('auctions.show', $auction->id()->value())
                ->with('success', 'Auction activated successfully!');
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * End an auction
     */
    public function end(int $id, EndAuctionAction $action): RedirectResponse
    {
        try {
            // First check if auction exists and user owns it
            $auction = $this->auctionRepository->findById(Id::fromString((string) $id));
            if (!$auction || $auction->user_id !== auth()->id()) {
                return redirect()
                    ->back()
                    ->withErrors(['error' => 'Auction not found']);
            }

            $userId = UserId::fromString((string) auth()->id());
            $auction = $action->execute($id, $userId);

            return redirect()
                ->route('auctions.show', $auction->id()->value())
                ->with('success', 'Auction ended successfully!');
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Upload images for an auction
     */
    public function uploadImages(
        int $id,
        UploadAuctionImagesRequest $request,
        UploadAuctionImagesAction $action
    ): RedirectResponse {
        try {
            $userId = UserId::fromString((string) auth()->id());
            $action->execute($id, $request->file('images'), $userId);

            return redirect()
                ->back()
                ->with('success', 'Images uploaded successfully!');
        } catch (\Exception $e) {
            return redirect()
                ->back()
                ->withErrors(['error' => $e->getMessage()]);
        }
    }
}

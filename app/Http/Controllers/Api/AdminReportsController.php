<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Domain\Auction\Repositories\AuctionRepositoryInterface;
use App\Domain\Payment\Repositories\PaymentRepositoryInterface;
use App\Domain\User\Repositories\UserRepositoryInterface;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Validator;

class AdminReportsController extends Controller
{
    public function __construct(
        private AuctionRepositoryInterface $auctionRepository,
        private PaymentRepositoryInterface $paymentRepository,
        private UserRepositoryInterface $userRepository
    ) {}

    /**
     * Export data in various formats
     */
    public function export(Request $request): JsonResponse|Response
    {
        $validator = Validator::make($request->all(), [
            'type' => ['required', 'string', 'in:users,auctions,payments,revenue,bids'],
            'format' => ['required', 'string', 'in:csv,json,xlsx'],
            'date_from' => ['nullable', 'date'],
            'date_to' => ['nullable', 'date', 'after_or_equal:date_from'],
            'filters' => ['nullable', 'array'],
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $type = $request->get('type');
            $format = $request->get('format');
            $dateFrom = $request->get('date_from');
            $dateTo = $request->get('date_to');
            $filters = $request->get('filters', []);

            $data = $this->getExportData($type, $dateFrom, $dateTo, $filters);
            
            if ($format === 'json') {
                return response()->json([
                    'data' => $data,
                    'meta' => [
                        'type' => $type,
                        'format' => $format,
                        'generated_at' => now()->toISOString(),
                        'record_count' => count($data),
                    ],
                ]);
            }

            // For CSV and XLSX, we would typically generate a file and return a download response
            // For now, return a JSON response with download URL
            $filename = $this->generateExportFile($data, $type, $format);

            return response()->json([
                'message' => 'Export generated successfully',
                'data' => [
                    'download_url' => url("storage/exports/{$filename}"),
                    'filename' => $filename,
                    'type' => $type,
                    'format' => $format,
                    'record_count' => count($data),
                    'expires_at' => now()->addHours(24)->toISOString(),
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to generate export',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get sales report
     */
    public function salesReport(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'period' => ['nullable', 'string', 'in:daily,weekly,monthly,quarterly,yearly'],
            'date_from' => ['nullable', 'date'],
            'date_to' => ['nullable', 'date', 'after_or_equal:date_from'],
            'category_id' => ['nullable', 'integer', 'exists:categories,id'],
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $period = $request->get('period', 'monthly');
            $dateFrom = $request->get('date_from', now()->subYear()->toDateString());
            $dateTo = $request->get('date_to', now()->toDateString());
            $categoryId = $request->get('category_id');

            $salesData = $this->auctionRepository->getSalesReport($period, $dateFrom, $dateTo, $categoryId);
            $summary = $this->auctionRepository->getSalesSummary($dateFrom, $dateTo, $categoryId);

            return response()->json([
                'data' => [
                    'sales_over_time' => $salesData,
                    'summary' => $summary,
                    'period' => $period,
                    'date_range' => [
                        'from' => $dateFrom,
                        'to' => $dateTo,
                    ],
                    'category_id' => $categoryId,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to generate sales report',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get user activity report
     */
    public function userActivityReport(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'date_from' => ['nullable', 'date'],
            'date_to' => ['nullable', 'date', 'after_or_equal:date_from'],
            'activity_type' => ['nullable', 'string', 'in:registrations,logins,auctions,bids'],
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $dateFrom = $request->get('date_from', now()->subMonth()->toDateString());
            $dateTo = $request->get('date_to', now()->toDateString());
            $activityType = $request->get('activity_type');

            $activityData = $this->userRepository->getActivityReport($dateFrom, $dateTo, $activityType);
            $summary = $this->userRepository->getActivitySummary($dateFrom, $dateTo);

            return response()->json([
                'data' => [
                    'activity_data' => $activityData,
                    'summary' => $summary,
                    'date_range' => [
                        'from' => $dateFrom,
                        'to' => $dateTo,
                    ],
                    'activity_type' => $activityType,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to generate user activity report',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get financial report
     */
    public function financialReport(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'period' => ['nullable', 'string', 'in:monthly,quarterly,yearly'],
            'date_from' => ['nullable', 'date'],
            'date_to' => ['nullable', 'date', 'after_or_equal:date_from'],
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $period = $request->get('period', 'monthly');
            $dateFrom = $request->get('date_from', now()->subYear()->toDateString());
            $dateTo = $request->get('date_to', now()->toDateString());

            $revenueData = $this->paymentRepository->getRevenueReport($period, $dateFrom, $dateTo);
            $expenseData = $this->getExpenseData($period, $dateFrom, $dateTo); // Would be implemented
            $profitData = $this->calculateProfitData($revenueData, $expenseData);

            return response()->json([
                'data' => [
                    'revenue' => $revenueData,
                    'expenses' => $expenseData,
                    'profit' => $profitData,
                    'period' => $period,
                    'date_range' => [
                        'from' => $dateFrom,
                        'to' => $dateTo,
                    ],
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to generate financial report',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get export data based on type
     */
    private function getExportData(string $type, ?string $dateFrom, ?string $dateTo, array $filters): array
    {
        switch ($type) {
            case 'users':
                return $this->userRepository->getExportData($dateFrom, $dateTo, $filters);
            case 'auctions':
                return $this->auctionRepository->getExportData($dateFrom, $dateTo, $filters);
            case 'payments':
                return $this->paymentRepository->getExportData($dateFrom, $dateTo, $filters);
            case 'revenue':
                return $this->paymentRepository->getRevenueExportData($dateFrom, $dateTo, $filters);
            default:
                throw new \InvalidArgumentException("Unsupported export type: {$type}");
        }
    }

    /**
     * Generate export file (placeholder implementation)
     */
    private function generateExportFile(array $data, string $type, string $format): string
    {
        $timestamp = now()->format('Y-m-d_H-i-s');
        $filename = "{$type}_export_{$timestamp}.{$format}";
        
        // In a real implementation, you would:
        // 1. Generate the actual file in the specified format
        // 2. Store it in storage/app/public/exports/
        // 3. Return the filename
        
        return $filename;
    }

    /**
     * Get expense data (placeholder implementation)
     */
    private function getExpenseData(string $period, string $dateFrom, string $dateTo): array
    {
        // This would typically come from an expense tracking system
        return [
            'hosting' => 1000,
            'payment_processing' => 500,
            'marketing' => 2000,
            'support' => 1500,
            'total' => 5000,
        ];
    }

    /**
     * Calculate profit data
     */
    private function calculateProfitData(array $revenueData, array $expenseData): array
    {
        $totalRevenue = $revenueData['total'] ?? 0;
        $totalExpenses = $expenseData['total'] ?? 0;
        
        return [
            'gross_profit' => $totalRevenue - $totalExpenses,
            'profit_margin' => $totalRevenue > 0 ? (($totalRevenue - $totalExpenses) / $totalRevenue) * 100 : 0,
            'revenue' => $totalRevenue,
            'expenses' => $totalExpenses,
        ];
    }
}

<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Domain\User\Repositories\UserRepositoryInterface;
use App\Domain\User\ValueObjects\Email;
use App\Domain\User\ValueObjects\UserId;
use App\Http\Controllers\Controller;
use App\Http\Resources\UserResource;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class AdminUserController extends Controller
{
    public function __construct(
        private UserRepositoryInterface $userRepository
    ) {}

    /**
     * Display a listing of users
     */
    public function index(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'per_page' => ['nullable', 'integer', 'min:1', 'max:100'],
            'search' => ['nullable', 'string', 'max:255'],
            'role' => ['nullable', 'string', 'in:admin,user'],
            'status' => ['nullable', 'string', 'in:active,inactive,verified,unverified'],
            'sort' => ['nullable', 'string', 'in:name,email,created_at,last_login_at'],
            'direction' => ['nullable', 'string', 'in:asc,desc'],
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $perPage = min($request->get('per_page', 15), 100);
        $search = $request->get('search');
        $role = $request->get('role');
        $status = $request->get('status');

        if ($search) {
            $users = $this->userRepository->search($search, $perPage);
        } elseif ($role) {
            $users = $this->userRepository->findByRole($role, $perPage);
        } elseif ($status === 'verified') {
            $users = $this->userRepository->findVerified($perPage);
        } elseif ($status === 'unverified') {
            $users = $this->userRepository->findUnverified($perPage);
        } else {
            $users = $this->userRepository->findAll($perPage);
        }

        return response()->json([
            'data' => UserResource::collection($users),
            'meta' => [
                'current_page' => $users->currentPage(),
                'last_page' => $users->lastPage(),
                'per_page' => $users->perPage(),
                'total' => $users->total(),
            ],
        ]);
    }

    /**
     * Display the specified user
     */
    public function show(int $id): JsonResponse
    {
        try {
            $userId = UserId::fromString((string) $id);
            $user = $this->userRepository->findByIdOrFail($userId);

            return response()->json([
                'data' => new UserResource($user),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'User not found',
                'error' => $e->getMessage(),
            ], 404);
        }
    }

    /**
     * Update the specified user
     */
    public function update(Request $request, int $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'name' => ['sometimes', 'string', 'max:255'],
            'email' => ['sometimes', 'email', 'max:255', 'unique:users,email,' . $id],
            'role' => ['sometimes', 'string', 'in:admin,user'],
            'is_active' => ['sometimes', 'boolean'],
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $userId = UserId::fromString((string) $id);
            $user = $this->userRepository->findByIdOrFail($userId);

            // Update user properties
            if ($request->has('name')) {
                $user->updateName($request->name);
            }

            if ($request->has('email')) {
                $email = Email::fromString($request->email);
                $user->updateEmail($email);
            }

            if ($request->has('role')) {
                $user->updateRole($request->role);
            }

            if ($request->has('is_active')) {
                if ($request->is_active) {
                    $user->activate();
                } else {
                    $user->deactivate();
                }
            }

            $this->userRepository->save($user);

            return response()->json([
                'message' => 'User updated successfully',
                'data' => new UserResource($user),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to update user',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Remove the specified user
     */
    public function destroy(int $id): JsonResponse
    {
        try {
            $userId = UserId::fromString((string) $id);
            $user = $this->userRepository->findByIdOrFail($userId);

            // Check if user has active auctions or bids
            $hasActiveAuctions = $this->userRepository->hasActiveAuctions($userId);
            $hasActiveBids = $this->userRepository->hasActiveBids($userId);

            if ($hasActiveAuctions || $hasActiveBids) {
                return response()->json([
                    'message' => 'Cannot delete user with active auctions or bids',
                ], 409);
            }

            $this->userRepository->delete($user);

            return response()->json([
                'message' => 'User deleted successfully',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to delete user',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Verify user's email
     */
    public function verify(int $id): JsonResponse
    {
        try {
            $userId = UserId::fromString((string) $id);
            $user = $this->userRepository->findByIdOrFail($userId);

            $user->markEmailAsVerified();
            $this->userRepository->save($user);

            return response()->json([
                'message' => 'User email verified successfully',
                'data' => new UserResource($user),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to verify user email',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Suspend user account
     */
    public function suspend(int $id): JsonResponse
    {
        try {
            $userId = UserId::fromString((string) $id);
            $user = $this->userRepository->findByIdOrFail($userId);

            $user->suspend();
            $this->userRepository->save($user);

            return response()->json([
                'message' => 'User suspended successfully',
                'data' => new UserResource($user),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to suspend user',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Unsuspend user account
     */
    public function unsuspend(int $id): JsonResponse
    {
        try {
            $userId = UserId::fromString((string) $id);
            $user = $this->userRepository->findByIdOrFail($userId);

            $user->unsuspend();
            $this->userRepository->save($user);

            return response()->json([
                'message' => 'User unsuspended successfully',
                'data' => new UserResource($user),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to unsuspend user',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get user statistics
     */
    public function statistics(): JsonResponse
    {
        try {
            $stats = $this->userRepository->getStatistics();

            return response()->json([
                'data' => $stats,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to get user statistics',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}

<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Domain\Auction\Repositories\AuctionRepositoryInterface;
use App\Domain\Auction\Repositories\BidRepositoryInterface;
use App\Domain\Payment\Repositories\PaymentRepositoryInterface;
use App\Domain\User\Repositories\UserRepositoryInterface;
use App\Http\Controllers\Controller;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class AdminStatisticsController extends Controller
{
    public function __construct(
        private AuctionRepositoryInterface $auctionRepository,
        private BidRepositoryInterface $bidRepository,
        private PaymentRepositoryInterface $paymentRepository,
        private UserRepositoryInterface $userRepository
    ) {}

    /**
     * Get overview statistics
     */
    public function overview(): JsonResponse
    {
        try {
            $auctionStats = $this->auctionRepository->getStatistics();
            $userStats = $this->userRepository->getStatistics();
            $paymentStats = $this->paymentRepository->getStatistics();

            $overview = [
                'users' => [
                    'total' => $userStats['total_users'] ?? 0,
                    'active' => $userStats['active_users'] ?? 0,
                    'new_this_month' => $userStats['new_users_this_month'] ?? 0,
                    'verified' => $userStats['verified_users'] ?? 0,
                ],
                'auctions' => [
                    'total' => $auctionStats['total_auctions'] ?? 0,
                    'active' => $auctionStats['active_auctions'] ?? 0,
                    'ended' => $auctionStats['ended_auctions'] ?? 0,
                    'total_value' => $auctionStats['total_value'] ?? 0,
                ],
                'payments' => [
                    'total_revenue' => $paymentStats['total_revenue'] ?? 0,
                    'completed_payments' => $paymentStats['completed_payments'] ?? 0,
                    'failed_payments' => $paymentStats['failed_payments'] ?? 0,
                    'pending_payments' => $paymentStats['pending_payments'] ?? 0,
                ],
                'bids' => [
                    'total_bids' => $auctionStats['total_bids'] ?? 0,
                    'average_bids_per_auction' => $auctionStats['average_bids_per_auction'] ?? 0,
                ],
            ];

            return response()->json([
                'data' => $overview,
                'generated_at' => now()->toISOString(),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to get overview statistics',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get revenue statistics
     */
    public function revenue(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'period' => ['nullable', 'string', 'in:daily,weekly,monthly,yearly'],
            'date_from' => ['nullable', 'date'],
            'date_to' => ['nullable', 'date', 'after_or_equal:date_from'],
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $period = $request->get('period', 'monthly');
            $dateFrom = $request->get('date_from', now()->subYear()->toDateString());
            $dateTo = $request->get('date_to', now()->toDateString());

            $revenueData = $this->paymentRepository->getRevenueAnalytics($period, $dateFrom, $dateTo);
            $revenueStats = $this->paymentRepository->getRevenueStatistics($dateFrom, $dateTo);

            return response()->json([
                'data' => [
                    'revenue_over_time' => $revenueData,
                    'statistics' => $revenueStats,
                    'period' => $period,
                    'date_range' => [
                        'from' => $dateFrom,
                        'to' => $dateTo,
                    ],
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to get revenue statistics',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get user statistics
     */
    public function users(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'period' => ['nullable', 'string', 'in:daily,weekly,monthly'],
            'date_from' => ['nullable', 'date'],
            'date_to' => ['nullable', 'date', 'after_or_equal:date_from'],
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $period = $request->get('period', 'monthly');
            $dateFrom = $request->get('date_from', now()->subYear()->toDateString());
            $dateTo = $request->get('date_to', now()->toDateString());

            $userGrowth = $this->userRepository->getUserGrowthAnalytics($period, $dateFrom, $dateTo);
            $userStats = $this->userRepository->getDetailedStatistics();
            $topSellers = $this->userRepository->findTopSellers(10);

            return response()->json([
                'data' => [
                    'user_growth' => $userGrowth,
                    'statistics' => $userStats,
                    'top_sellers' => $topSellers,
                    'period' => $period,
                    'date_range' => [
                        'from' => $dateFrom,
                        'to' => $dateTo,
                    ],
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to get user statistics',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get auction statistics
     */
    public function auctions(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'period' => ['nullable', 'string', 'in:daily,weekly,monthly'],
            'date_from' => ['nullable', 'date'],
            'date_to' => ['nullable', 'date', 'after_or_equal:date_from'],
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $period = $request->get('period', 'monthly');
            $dateFrom = $request->get('date_from', now()->subYear()->toDateString());
            $dateTo = $request->get('date_to', now()->toDateString());

            $auctionGrowth = $this->auctionRepository->getAuctionGrowthAnalytics($period, $dateFrom, $dateTo);
            $auctionStats = $this->auctionRepository->getDetailedStatistics();
            $categoryStats = $this->auctionRepository->getCategoryStatistics();

            return response()->json([
                'data' => [
                    'auction_growth' => $auctionGrowth,
                    'statistics' => $auctionStats,
                    'category_breakdown' => $categoryStats,
                    'period' => $period,
                    'date_range' => [
                        'from' => $dateFrom,
                        'to' => $dateTo,
                    ],
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to get auction statistics',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get platform health metrics
     */
    public function health(): JsonResponse
    {
        try {
            // Get various health metrics
            $activeUsers = $this->userRepository->countActiveUsers();
            $activeAuctions = $this->auctionRepository->countByStatus('active');
            $pendingPayments = $this->paymentRepository->countByStatus('pending');
            $failedPayments = $this->paymentRepository->countByStatus('failed');
            
            // Calculate health scores (0-100)
            $userEngagement = min(100, ($activeUsers / max(1, $this->userRepository->count())) * 100);
            $auctionActivity = min(100, ($activeAuctions / max(1, $this->auctionRepository->count())) * 100);
            $paymentHealth = max(0, 100 - (($failedPayments / max(1, $pendingPayments + $failedPayments)) * 100));

            $overallHealth = ($userEngagement + $auctionActivity + $paymentHealth) / 3;

            return response()->json([
                'data' => [
                    'overall_health' => round($overallHealth, 2),
                    'metrics' => [
                        'user_engagement' => round($userEngagement, 2),
                        'auction_activity' => round($auctionActivity, 2),
                        'payment_health' => round($paymentHealth, 2),
                    ],
                    'raw_data' => [
                        'active_users' => $activeUsers,
                        'total_users' => $this->userRepository->count(),
                        'active_auctions' => $activeAuctions,
                        'total_auctions' => $this->auctionRepository->count(),
                        'pending_payments' => $pendingPayments,
                        'failed_payments' => $failedPayments,
                    ],
                ],
                'generated_at' => now()->toISOString(),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to get platform health metrics',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}

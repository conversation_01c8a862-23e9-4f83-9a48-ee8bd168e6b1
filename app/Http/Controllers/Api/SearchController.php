<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Domain\Auction\Repositories\AuctionRepositoryInterface;
use App\Domain\Auction\Repositories\CategoryRepositoryInterface;
use App\Http\Controllers\Controller;
use App\Http\Resources\AuctionCollection;
use App\Http\Resources\CategoryResource;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class SearchController extends Controller
{
    public function __construct(
        private AuctionRepositoryInterface $auctionRepository,
        private CategoryRepositoryInterface $categoryRepository
    ) {}

    /**
     * Search auctions
     */
    public function search(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'q' => ['required', 'string', 'min:2', 'max:255'],
            'category_id' => ['nullable', 'integer', 'exists:categories,id'],
            'status' => ['nullable', 'string', 'in:active,ended,scheduled'],
            'min_price' => ['nullable', 'numeric', 'min:0'],
            'max_price' => ['nullable', 'numeric', 'min:0'],
            'condition' => ['nullable', 'string', 'in:new,like_new,good,fair,poor'],
            'sort' => ['nullable', 'string', 'in:relevance,price_asc,price_desc,ending_soon,newest'],
            'per_page' => ['nullable', 'integer', 'min:1', 'max:50'],
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $query = $request->input('q');
        $perPage = min($request->get('per_page', 15), 50);
        
        $filters = array_filter([
            'category_id' => $request->input('category_id'),
            'status' => $request->input('status', 'active'),
            'min_price' => $request->input('min_price'),
            'max_price' => $request->input('max_price'),
            'condition' => $request->input('condition'),
        ]);

        $auctions = $this->auctionRepository->search($query, $filters, $perPage);

        return response()->json([
            'data' => new AuctionCollection($auctions),
            'meta' => [
                'query' => $query,
                'filters' => $filters,
                'total_results' => $auctions->total(),
            ],
        ]);
    }

    /**
     * Get search suggestions
     */
    public function suggestions(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'q' => ['required', 'string', 'min:1', 'max:100'],
            'limit' => ['nullable', 'integer', 'min:1', 'max:20'],
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $query = $request->input('q');
        $limit = $request->input('limit', 10);

        // Search categories that match the query
        $categories = $this->categoryRepository->searchByName($query)
            ->take($limit / 2);

        // Get recent popular search terms (this would typically come from a search analytics service)
        $popularTerms = $this->getPopularSearchTerms($query, $limit / 2);

        return response()->json([
            'data' => [
                'categories' => CategoryResource::collection($categories),
                'popular_terms' => $popularTerms,
                'query' => $query,
            ],
        ]);
    }

    /**
     * Get popular search terms (placeholder implementation)
     */
    private function getPopularSearchTerms(string $query, int $limit): array
    {
        // This would typically query a search analytics database or cache
        // For now, return some mock popular terms
        $mockTerms = [
            'vintage watches',
            'antique furniture',
            'collectible coins',
            'art paintings',
            'jewelry rings',
            'electronics phones',
            'books rare',
            'toys vintage',
            'cars classic',
            'sports memorabilia',
        ];

        return collect($mockTerms)
            ->filter(fn($term) => str_contains(strtolower($term), strtolower($query)))
            ->take($limit)
            ->values()
            ->toArray();
    }

    /**
     * Get search filters metadata
     */
    public function filters(): JsonResponse
    {
        $categories = $this->categoryRepository->findActive();
        
        $conditions = [
            ['value' => 'new', 'label' => 'New'],
            ['value' => 'like_new', 'label' => 'Like New'],
            ['value' => 'good', 'label' => 'Good'],
            ['value' => 'fair', 'label' => 'Fair'],
            ['value' => 'poor', 'label' => 'Poor'],
        ];

        $sortOptions = [
            ['value' => 'relevance', 'label' => 'Most Relevant'],
            ['value' => 'ending_soon', 'label' => 'Ending Soon'],
            ['value' => 'newest', 'label' => 'Newest First'],
            ['value' => 'price_asc', 'label' => 'Price: Low to High'],
            ['value' => 'price_desc', 'label' => 'Price: High to Low'],
        ];

        return response()->json([
            'data' => [
                'categories' => CategoryResource::collection($categories),
                'conditions' => $conditions,
                'sort_options' => $sortOptions,
            ],
        ]);
    }

    /**
     * Get trending searches
     */
    public function trending(): JsonResponse
    {
        // This would typically come from search analytics
        $trendingTerms = [
            'vintage watches',
            'antique furniture',
            'collectible coins',
            'art paintings',
            'jewelry',
        ];

        return response()->json([
            'data' => [
                'trending_terms' => $trendingTerms,
                'updated_at' => now()->toISOString(),
            ],
        ]);
    }
}

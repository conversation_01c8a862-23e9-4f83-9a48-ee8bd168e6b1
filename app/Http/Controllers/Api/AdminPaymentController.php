<?php

declare(strict_types=1);

namespace App\Http\Controllers\Api;

use App\Domain\Payment\Repositories\PaymentRepositoryInterface;
use App\Domain\Payment\ValueObjects\PaymentStatus;
use App\Domain\Shared\ValueObjects\Id;
use App\Http\Controllers\Controller;
use App\Http\Resources\PaymentResource;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class AdminPaymentController extends Controller
{
    public function __construct(
        private PaymentRepositoryInterface $paymentRepository
    ) {}

    /**
     * Display a listing of payments
     */
    public function index(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'per_page' => ['nullable', 'integer', 'min:1', 'max:100'],
            'status' => ['nullable', 'string', 'in:pending,processing,completed,failed,refunded'],
            'user_id' => ['nullable', 'integer', 'exists:users,id'],
            'auction_id' => ['nullable', 'integer', 'exists:auctions,id'],
            'date_from' => ['nullable', 'date'],
            'date_to' => ['nullable', 'date', 'after_or_equal:date_from'],
            'sort' => ['nullable', 'string', 'in:created_at,amount,status'],
            'direction' => ['nullable', 'string', 'in:asc,desc'],
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        $perPage = min($request->get('per_page', 15), 100);
        $status = $request->get('status');
        $userId = $request->get('user_id');
        $auctionId = $request->get('auction_id');
        $dateFrom = $request->get('date_from');
        $dateTo = $request->get('date_to');

        $filters = array_filter([
            'status' => $status,
            'user_id' => $userId,
            'auction_id' => $auctionId,
            'date_from' => $dateFrom,
            'date_to' => $dateTo,
        ]);

        if ($status) {
            $paymentStatus = PaymentStatus::fromString($status);
            $payments = $this->paymentRepository->findByStatus($paymentStatus, $perPage);
        } elseif ($userId) {
            $userIdVO = \App\Domain\User\ValueObjects\UserId::fromString((string) $userId);
            $payments = $this->paymentRepository->findByUser($userIdVO, $perPage);
        } elseif ($auctionId) {
            $auctionIdVO = Id::fromString((string) $auctionId);
            $payments = $this->paymentRepository->findByAuction($auctionIdVO, $perPage);
        } else {
            $payments = $this->paymentRepository->findAll($perPage);
        }

        return response()->json([
            'data' => PaymentResource::collection($payments),
            'meta' => [
                'current_page' => $payments->currentPage(),
                'last_page' => $payments->lastPage(),
                'per_page' => $payments->perPage(),
                'total' => $payments->total(),
                'filters' => $filters,
            ],
        ]);
    }

    /**
     * Display the specified payment
     */
    public function show(int $id): JsonResponse
    {
        try {
            $paymentId = Id::fromString((string) $id);
            $payment = $this->paymentRepository->findByIdOrFail($paymentId);

            return response()->json([
                'data' => new PaymentResource($payment),
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Payment not found',
                'error' => $e->getMessage(),
            ], 404);
        }
    }

    /**
     * Get payment statistics
     */
    public function statistics(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'period' => ['nullable', 'string', 'in:today,week,month,quarter,year'],
            'date_from' => ['nullable', 'date'],
            'date_to' => ['nullable', 'date', 'after_or_equal:date_from'],
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $period = $request->get('period', 'month');
            $dateFrom = $request->get('date_from');
            $dateTo = $request->get('date_to');

            $stats = $this->paymentRepository->getStatistics($period, $dateFrom, $dateTo);

            return response()->json([
                'data' => $stats,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to get payment statistics',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get revenue analytics
     */
    public function revenue(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'period' => ['nullable', 'string', 'in:daily,weekly,monthly'],
            'date_from' => ['nullable', 'date'],
            'date_to' => ['nullable', 'date', 'after_or_equal:date_from'],
        ]);

        if ($validator->fails()) {
            return response()->json([
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        try {
            $period = $request->get('period', 'daily');
            $dateFrom = $request->get('date_from', now()->subDays(30)->toDateString());
            $dateTo = $request->get('date_to', now()->toDateString());

            $revenue = $this->paymentRepository->getRevenueAnalytics($period, $dateFrom, $dateTo);

            return response()->json([
                'data' => $revenue,
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to get revenue analytics',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get failed payments that need attention
     */
    public function failed(Request $request): JsonResponse
    {
        $perPage = min($request->get('per_page', 15), 100);
        
        try {
            $failedStatus = PaymentStatus::failed();
            $payments = $this->paymentRepository->findByStatus($failedStatus, $perPage);

            return response()->json([
                'data' => PaymentResource::collection($payments),
                'meta' => [
                    'current_page' => $payments->currentPage(),
                    'last_page' => $payments->lastPage(),
                    'per_page' => $payments->perPage(),
                    'total' => $payments->total(),
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to get failed payments',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get payments stuck in processing
     */
    public function stuckInProcessing(Request $request): JsonResponse
    {
        $minutes = $request->get('minutes', 30);
        $perPage = min($request->get('per_page', 15), 100);
        
        try {
            $payments = $this->paymentRepository->findStuckInProcessing($minutes);

            return response()->json([
                'data' => PaymentResource::collection($payments->take($perPage)),
                'meta' => [
                    'total' => $payments->count(),
                    'minutes_threshold' => $minutes,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to get stuck payments',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get refund requests
     */
    public function refunds(Request $request): JsonResponse
    {
        $perPage = min($request->get('per_page', 15), 100);
        
        try {
            $refundedStatus = PaymentStatus::refunded();
            $payments = $this->paymentRepository->findByStatus($refundedStatus, $perPage);

            return response()->json([
                'data' => PaymentResource::collection($payments),
                'meta' => [
                    'current_page' => $payments->currentPage(),
                    'last_page' => $payments->lastPage(),
                    'per_page' => $payments->perPage(),
                    'total' => $payments->total(),
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Failed to get refunds',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}

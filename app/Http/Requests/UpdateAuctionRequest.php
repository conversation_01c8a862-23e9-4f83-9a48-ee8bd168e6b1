<?php

declare(strict_types=1);

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateAuctionRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            'title' => 'sometimes|required|string|max:500',
            'description' => 'sometimes|required|string|max:10000',
            'condition' => 'sometimes|nullable|string|in:new,like_new,good,fair,poor',
            'starting_price' => 'sometimes|required|numeric|min:0.01|max:1000000',
            'reserve_price' => 'sometimes|nullable|numeric|min:0.01|max:1000000',
            'buyout_price' => 'sometimes|nullable|numeric|min:0.01|max:1000000',
            'currency' => 'sometimes|nullable|string|in:USD,EUR,GBP,CAD',
            'shipping_cost' => 'sometimes|nullable|numeric|min:0|max:1000',
            'auto_extend' => 'sometimes|nullable|boolean',
            'extend_minutes' => 'sometimes|nullable|integer|min:1|max:60',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'title.required' => 'Please provide a title for your auction.',
            'title.max' => 'The title cannot exceed 500 characters.',
            'description.required' => 'Please provide a description for your auction.',
            'description.max' => 'The description cannot exceed 10,000 characters.',
            'starting_price.required' => 'Please set a starting price for your auction.',
            'starting_price.min' => 'The starting price must be at least $0.01.',
            'starting_price.max' => 'The starting price cannot exceed $1,000,000.',
            'reserve_price.min' => 'The reserve price must be at least $0.01.',
            'buyout_price.min' => 'The buyout price must be at least $0.01.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Additional business rule validations
            if ($this->has(['reserve_price', 'starting_price']) && 
                $this->reserve_price && $this->starting_price &&
                $this->reserve_price < $this->starting_price) {
                $validator->errors()->add(
                    'reserve_price',
                    'Reserve price must be greater than or equal to starting price'
                );
            }

            if ($this->has(['buyout_price', 'starting_price']) && 
                $this->buyout_price && $this->starting_price &&
                $this->buyout_price <= $this->starting_price) {
                $validator->errors()->add(
                    'buyout_price',
                    'Buyout price must be greater than starting price'
                );
            }
        });
    }
}

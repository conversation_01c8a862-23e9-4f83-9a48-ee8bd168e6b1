<?php

declare(strict_types=1);

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UploadAuctionImagesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return auth()->check();
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        $maxFileSize = config('auction.images.max_file_size_kb', 5120);
        $allowedMimeTypes = implode(',', config('auction.images.allowed_mime_types', [
            'image/jpeg',
            'image/png',
            'image/gif',
            'image/webp',
        ]));

        return [
            'images' => 'required|array|min:1|max:10',
            'images.*' => [
                'required',
                'file',
                'image',
                "max:{$maxFileSize}",
                "mimes:jpeg,jpg,png,gif,webp",
                "mimetypes:{$allowedMimeTypes}",
                'dimensions:min_width=100,min_height=100,max_width=4000,max_height=4000',
            ],
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        $maxFileSize = config('auction.images.max_file_size_kb', 5120);
        $maxFileSizeMB = round($maxFileSize / 1024, 1);

        return [
            'images.required' => 'Please select at least one image to upload.',
            'images.array' => 'Invalid image data format.',
            'images.min' => 'Please select at least one image to upload.',
            'images.max' => 'You can upload a maximum of 10 images.',
            'images.*.required' => 'Each image file is required.',
            'images.*.file' => 'Each upload must be a valid file.',
            'images.*.image' => 'Each file must be a valid image.',
            'images.*.max' => "Each image must be smaller than {$maxFileSizeMB}MB.",
            'images.*.mimes' => 'Images must be in JPEG, PNG, GIF, or WebP format.',
            'images.*.mimetypes' => 'Images must be in JPEG, PNG, GIF, or WebP format.',
            'images.*.dimensions' => 'Images must be between 100x100 and 4000x4000 pixels.',
        ];
    }

    /**
     * Configure the validator instance.
     */
    public function withValidator($validator): void
    {
        $validator->after(function ($validator) {
            // Additional validation for total file size
            if ($this->hasFile('images')) {
                $totalSize = 0;
                foreach ($this->file('images') as $file) {
                    if ($file && $file->isValid()) {
                        $totalSize += $file->getSize();
                    }
                }

                $maxTotalSize = 50 * 1024 * 1024; // 50MB total
                if ($totalSize > $maxTotalSize) {
                    $validator->errors()->add(
                        'images',
                        'Total size of all images cannot exceed 50MB.'
                    );
                }
            }
        });
    }
}

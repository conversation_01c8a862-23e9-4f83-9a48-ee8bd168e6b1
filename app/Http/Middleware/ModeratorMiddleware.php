<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Symfony\Component\HttpFoundation\Response as BaseResponse;

class ModeratorMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): BaseResponse
    {
        if (!auth()->check()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'message' => 'Authentication required',
                    'error' => 'Unauthenticated',
                ], 401);
            }
            
            return redirect()->route('login');
        }

        $user = auth()->user();
        
        // Check if user has moderator or admin role
        if (!in_array($user->role, ['admin', 'moderator'])) {
            if ($request->expectsJson()) {
                return response()->json([
                    'message' => 'Access denied. Moderator privileges required.',
                    'error' => 'Forbidden',
                ], 403);
            }
            
            abort(403, 'Access denied. Moderator privileges required.');
        }

        // Check if user account is active
        if (!$user->is_active) {
            if ($request->expectsJson()) {
                return response()->json([
                    'message' => 'Account is inactive',
                    'error' => 'Account Inactive',
                ], 403);
            }
            
            abort(403, 'Account is inactive');
        }

        return $next($request);
    }
}

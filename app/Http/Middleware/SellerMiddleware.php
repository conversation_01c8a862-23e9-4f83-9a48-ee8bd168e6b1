<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Symfony\Component\HttpFoundation\Response as BaseResponse;

class SellerMiddleware
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): BaseResponse
    {
        if (!auth()->check()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'message' => 'Authentication required',
                    'error' => 'Unauthenticated',
                ], 401);
            }
            
            return redirect()->route('login');
        }

        $user = auth()->user();
        
        // Check if user has seller privileges (seller, premium_seller, admin)
        if (!in_array($user->role, ['seller', 'premium_seller', 'admin'])) {
            if ($request->expectsJson()) {
                return response()->json([
                    'message' => 'Access denied. Seller privileges required.',
                    'error' => 'Forbidden',
                ], 403);
            }
            
            abort(403, 'Access denied. Seller privileges required.');
        }

        // Check if user account is active
        if (!$user->is_active) {
            if ($request->expectsJson()) {
                return response()->json([
                    'message' => 'Account is inactive',
                    'error' => 'Account Inactive',
                ], 403);
            }
            
            abort(403, 'Account is inactive');
        }

        // Check if email is verified
        if (!$user->hasVerifiedEmail()) {
            if ($request->expectsJson()) {
                return response()->json([
                    'message' => 'Email verification required to create auctions',
                    'error' => 'Email Not Verified',
                ], 403);
            }
            
            abort(403, 'Email verification required to create auctions');
        }

        return $next($request);
    }
}

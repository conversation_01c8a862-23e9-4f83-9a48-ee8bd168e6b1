<?php

declare(strict_types=1);

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class ValidateRouteParameters
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // Validate numeric ID parameters
        $numericParams = ['id', 'auctionId', 'bidId', 'categoryId', 'paymentId', 'userId'];
        
        foreach ($numericParams as $param) {
            $value = $request->route($param);
            
            if ($value !== null && !$this->isValidId($value)) {
                return $this->handleInvalidParameter($request, $param, $value);
            }
        }

        // Validate slug parameters
        $slugParams = ['slug'];
        
        foreach ($slugParams as $param) {
            $value = $request->route($param);
            
            if ($value !== null && !$this->isValidSlug($value)) {
                return $this->handleInvalidParameter($request, $param, $value);
            }
        }

        return $next($request);
    }

    /**
     * Check if the value is a valid ID (positive integer)
     */
    private function isValidId($value): bool
    {
        if (!is_numeric($value)) {
            return false;
        }

        $intValue = (int) $value;
        
        // Must be a positive integer and the string representation must match
        return $intValue > 0 && (string) $intValue === (string) $value;
    }

    /**
     * Check if the value is a valid slug
     */
    private function isValidSlug($value): bool
    {
        if (!is_string($value) || empty(trim($value))) {
            return false;
        }

        // Basic slug validation: alphanumeric, hyphens, underscores
        return preg_match('/^[a-zA-Z0-9\-_]+$/', $value) === 1;
    }

    /**
     * Handle invalid parameter
     */
    private function handleInvalidParameter(Request $request, string $param, $value): Response
    {
        if ($request->expectsJson()) {
            return response()->json([
                'message' => 'Invalid parameter',
                'error' => "The {$param} parameter must be a valid value",
                'parameter' => $param,
                'value' => $value,
            ], 400);
        }

        abort(404, 'Resource not found');
    }
}

<?php

declare(strict_types=1);

namespace App\Infrastructure\Repositories;

use App\Domain\Auction\Models\Bid as DomainBid;
use App\Domain\Auction\Repositories\BidRepositoryInterface;
use App\Domain\Auction\ValueObjects\BidAmount;
use App\Domain\Shared\ValueObjects\Id;
use App\Domain\Shared\ValueObjects\Money;
use App\Domain\Shared\ValueObjects\Timestamp;
use App\Domain\User\ValueObjects\UserId;
use App\Models\Bid;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class EloquentBidRepository implements BidRepositoryInterface
{
    public function findById(Id $id): ?DomainBid
    {
        $bid = Bid::find($id->value());
        return $bid ? $this->toDomainModel($bid) : null;
    }

    public function findByIdOrFail(Id $id): DomainBid
    {
        $bid = Bid::findOrFail($id->value());
        return $this->toDomainModel($bid);
    }

    public function save(DomainBid $bid): void
    {
        $eloquentBid = $this->toEloquentModel($bid);
        $eloquentBid->save();
    }

    public function delete(DomainBid $bid): void
    {
        Bid::destroy($bid->id()->value());
    }

    public function findByAuction(Id $auctionId, int $perPage = 15): LengthAwarePaginator
    {
        return Bid::where('auction_id', $auctionId->value())
            ->with(['bidder'])
            ->orderBy('amount', 'desc')
            ->orderBy('timestamp', 'asc')
            ->paginate($perPage);
    }

    public function findByBidder(UserId $bidderId, int $perPage = 15): LengthAwarePaginator
    {
        return Bid::where('user_id', $bidderId->value())
            ->with(['auction', 'auction.category'])
            ->orderBy('timestamp', 'desc')
            ->paginate($perPage);
    }

    public function findHighestBidForAuction(Id $auctionId): ?DomainBid
    {
        $bid = Bid::where('auction_id', $auctionId->value())
            ->where('is_valid', true)
            ->orderBy('amount', 'desc')
            ->first();

        return $bid ? $this->toDomainModel($bid) : null;
    }

    public function findWinningBidForAuction(Id $auctionId): ?DomainBid
    {
        $bid = Bid::where('auction_id', $auctionId->value())
            ->where('is_winning', true)
            ->where('is_valid', true)
            ->first();

        return $bid ? $this->toDomainModel($bid) : null;
    }

    public function findUserHighestBidForAuction(Id $auctionId, UserId $bidderId): ?DomainBid
    {
        $bid = Bid::where('auction_id', $auctionId->value())
            ->where('user_id', $bidderId->value())
            ->where('is_valid', true)
            ->orderBy('amount', 'desc')
            ->first();

        return $bid ? $this->toDomainModel($bid) : null;
    }

    public function findAllBidsForAuction(Id $auctionId): Collection
    {
        return Bid::where('auction_id', $auctionId->value())
            ->with(['bidder'])
            ->orderBy('amount', 'desc')
            ->orderBy('timestamp', 'asc')
            ->get();
    }

    public function findValidBidsForAuction(Id $auctionId): Collection
    {
        return Bid::where('auction_id', $auctionId->value())
            ->where('is_valid', true)
            ->with(['bidder'])
            ->orderBy('amount', 'desc')
            ->get();
    }

    public function findProxyBidsForAuction(Id $auctionId, BidAmount $currentBid): Collection
    {
        return Bid::where('auction_id', $auctionId->value())
            ->where('bid_type', 'proxy')
            ->where('is_valid', true)
            ->where('max_bid', '>', $currentBid->amount())
            ->orderBy('max_bid', 'desc')
            ->get();
    }

    public function findBidsToMarkAsOutbid(Id $auctionId, Id $excludeBidId): Collection
    {
        return Bid::where('auction_id', $auctionId->value())
            ->where('id', '!=', $excludeBidId->value())
            ->where('is_winning', true)
            ->where('is_valid', true)
            ->get();
    }

    public function countBidsForAuction(Id $auctionId): int
    {
        return Bid::where('auction_id', $auctionId->value())->count();
    }

    public function countValidBidsForAuction(Id $auctionId): int
    {
        return Bid::where('auction_id', $auctionId->value())
            ->where('is_valid', true)
            ->count();
    }

    public function findUserBidHistory(UserId $bidderId, int $perPage = 15): LengthAwarePaginator
    {
        return Bid::where('user_id', $bidderId->value())
            ->with(['auction', 'auction.category', 'auction.seller'])
            ->orderBy('timestamp', 'desc')
            ->paginate($perPage);
    }

    public function findUserWinningBids(UserId $bidderId, int $perPage = 15): LengthAwarePaginator
    {
        return Bid::where('user_id', $bidderId->value())
            ->where('is_winning', true)
            ->where('is_valid', true)
            ->with(['auction', 'auction.category'])
            ->orderBy('timestamp', 'desc')
            ->paginate($perPage);
    }

    public function findUserOutbidBids(UserId $bidderId, int $perPage = 15): LengthAwarePaginator
    {
        return Bid::where('user_id', $bidderId->value())
            ->where('is_winning', false)
            ->where('is_valid', true)
            ->with(['auction', 'auction.category'])
            ->orderBy('timestamp', 'desc')
            ->paginate($perPage);
    }

    public function hasUserBidOnAuction(Id $auctionId, UserId $bidderId): bool
    {
        return Bid::where('auction_id', $auctionId->value())
            ->where('user_id', $bidderId->value())
            ->where('is_valid', true)
            ->exists();
    }

    public function getBidStatisticsForAuction(Id $auctionId): array
    {
        $bids = Bid::where('auction_id', $auctionId->value())
            ->where('is_valid', true);

        return [
            'total_bids' => $bids->count(),
            'unique_bidders' => $bids->distinct('user_id')->count(),
            'highest_bid' => $bids->max('amount'),
            'average_bid' => $bids->avg('amount'),
            'bid_increment_avg' => $this->calculateAverageBidIncrement($auctionId),
        ];
    }

    public function getUserBidStatistics(UserId $bidderId): array
    {
        $bids = Bid::where('user_id', $bidderId->value())
            ->where('is_valid', true);

        return [
            'total_bids' => $bids->count(),
            'auctions_bid_on' => $bids->distinct('auction_id')->count(),
            'winning_bids' => $bids->where('is_winning', true)->count(),
            'total_bid_amount' => $bids->sum('amount'),
            'average_bid' => $bids->avg('amount'),
            'highest_bid' => $bids->max('amount'),
        ];
    }

    public function markOtherBidsAsNotWinning(Id $auctionId, Id $winningBidId): void
    {
        Bid::where('auction_id', $auctionId->value())
            ->where('id', '!=', $winningBidId->value())
            ->update(['is_winning' => false]);
    }

    public function invalidateBids(Collection $bidIds, string $reason): void
    {
        Bid::whereIn('id', $bidIds->toArray())
            ->update([
                'is_valid' => false,
                'is_winning' => false,
                'invalidated_at' => now(),
                'invalidation_reason' => $reason,
            ]);
    }

    public function findRecentBidsForAuction(Id $auctionId, int $minutes = 5): Collection
    {
        return Bid::where('auction_id', $auctionId->value())
            ->where('timestamp', '>=', now()->subMinutes($minutes))
            ->where('is_valid', true)
            ->with(['bidder'])
            ->orderBy('timestamp', 'desc')
            ->get();
    }

    public function countUserActiveBids(UserId $userId): int
    {
        return Bid::where('user_id', $userId->value())
            ->whereHas('auction', function ($query) {
                $query->where('status', 'active');
            })
            ->where('is_valid', true)
            ->count();
    }

    public function findUserRecent(UserId $userId, int $limit = 10): Collection
    {
        return Bid::where('user_id', $userId->value())
            ->with(['auction', 'auction.category'])
            ->orderBy('timestamp', 'desc')
            ->limit($limit)
            ->get();
    }

    private function calculateAverageBidIncrement(Id $auctionId): float
    {
        $bids = Bid::where('auction_id', $auctionId->value())
            ->where('is_valid', true)
            ->orderBy('timestamp', 'asc')
            ->pluck('amount')
            ->toArray();

        if (count($bids) < 2) {
            return 0.0;
        }

        $increments = [];
        for ($i = 1; $i < count($bids); $i++) {
            $increments[] = $bids[$i] - $bids[$i - 1];
        }

        return array_sum($increments) / count($increments);
    }

    private function toDomainModel(Bid $bid): DomainBid
    {
        // Convert Eloquent model to Domain model
        $id = Id::fromString((string) $bid->id);
        $auctionId = Id::fromString((string) $bid->auction_id);
        $bidderId = UserId::fromString((string) $bid->user_id);
        $amount = BidAmount::fromFloat($bid->amount, 'USD');
        $maxBid = $bid->max_bid ? new Money($bid->max_bid, 'USD') : null;

        // Create the domain bid using the constructor
        $domainBid = new DomainBid(
            $id,
            $auctionId,
            $bidderId,
            $amount,
            $bid->bid_type,
            $maxBid,
            $bid->ip_address,
            $bid->user_agent
        );

        // Set additional properties using reflection
        $this->setDomainBidProperties($domainBid, $bid);

        return $domainBid;
    }

    private function toEloquentModel(DomainBid $bid): Bid
    {
        // Convert Domain model to Eloquent model
        $eloquentBid = new Bid();

        $eloquentBid->id = $bid->id()->value();
        $eloquentBid->auction_id = $bid->auctionId()->value();
        $eloquentBid->user_id = $bid->bidderId()->value();
        $eloquentBid->amount = $bid->amount()->amount();
        $eloquentBid->max_bid = $bid->maxBid()?->amount();
        $eloquentBid->bid_type = $bid->type();
        $eloquentBid->timestamp = $bid->timestamp()->value();
        $eloquentBid->is_winning = $bid->isWinning();
        $eloquentBid->is_valid = $bid->isValid();
        $eloquentBid->invalidated_at = $bid->invalidatedAt()?->value();
        $eloquentBid->invalidation_reason = $bid->invalidationReason();
        $eloquentBid->ip_address = $bid->ipAddress();
        $eloquentBid->user_agent = $bid->userAgent();
        $eloquentBid->created_at = $bid->createdAt()->value();
        $eloquentBid->updated_at = $bid->updatedAt()->value();

        return $eloquentBid;
    }

    private function setDomainBidProperties(DomainBid $domainBid, Bid $bid): void
    {
        // Use reflection to set private properties that aren't available through setters
        $reflection = new \ReflectionClass($domainBid);

        // Set timestamp
        $timestampProperty = $reflection->getProperty('timestamp');
        $timestampProperty->setAccessible(true);
        $timestampProperty->setValue($domainBid, Timestamp::fromString($bid->timestamp->toISOString()));

        // Set winning status
        $isWinningProperty = $reflection->getProperty('isWinning');
        $isWinningProperty->setAccessible(true);
        $isWinningProperty->setValue($domainBid, $bid->is_winning);

        // Set valid status
        $isValidProperty = $reflection->getProperty('isValid');
        $isValidProperty->setAccessible(true);
        $isValidProperty->setValue($domainBid, $bid->is_valid);

        // Set invalidation details if applicable
        if ($bid->invalidated_at) {
            $invalidatedAtProperty = $reflection->getProperty('invalidatedAt');
            $invalidatedAtProperty->setAccessible(true);
            $invalidatedAtProperty->setValue($domainBid, Timestamp::fromString($bid->invalidated_at->toISOString()));
        }

        if ($bid->invalidation_reason) {
            $invalidationReasonProperty = $reflection->getProperty('invalidationReason');
            $invalidationReasonProperty->setAccessible(true);
            $invalidationReasonProperty->setValue($domainBid, $bid->invalidation_reason);
        }

        // Set timestamps
        $createdAtProperty = $reflection->getProperty('createdAt');
        $createdAtProperty->setAccessible(true);
        $createdAtProperty->setValue($domainBid, Timestamp::fromString($bid->created_at->toISOString()));

        $updatedAtProperty = $reflection->getProperty('updatedAt');
        $updatedAtProperty->setAccessible(true);
        $updatedAtProperty->setValue($domainBid, Timestamp::fromString($bid->updated_at->toISOString()));
    }
}

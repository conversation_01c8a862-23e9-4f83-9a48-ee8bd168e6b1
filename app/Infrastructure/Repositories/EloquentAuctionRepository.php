<?php

declare(strict_types=1);

namespace App\Infrastructure\Repositories;

use App\Domain\Auction\Models\Auction as DomainAuction;
use App\Models\Auction as EloquentAuction;
use App\Domain\Auction\Repositories\AuctionRepositoryInterface;
use App\Domain\Auction\ValueObjects\AuctionDuration;
use App\Domain\Auction\ValueObjects\AuctionStatus;
use App\Domain\Auction\ValueObjects\BidAmount;
use App\Domain\Auction\ValueObjects\ReservePrice;
use App\Domain\Shared\ValueObjects\Id;
use App\Domain\Shared\ValueObjects\Money;
use App\Domain\Shared\ValueObjects\Timestamp;
use App\Domain\User\ValueObjects\UserId;
use App\Models\Auction;
use Illuminate\Contracts\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class EloquentAuctionRepository implements AuctionRepositoryInterface
{
    public function findById(Id $id): ?EloquentAuction
    {
        return Auction::find($id->value());
    }

    public function findByIdOrFail(Id $id): EloquentAuction
    {
        return Auction::findOrFail($id->value());
    }

    public function save(EloquentAuction $auction): void
    {
        $auction->save();
    }

    public function delete(EloquentAuction $auction): void
    {
        $auction->delete();
    }

    public function findBySeller(UserId $sellerId, int $perPage = 15): LengthAwarePaginator
    {
        return Auction::where('user_id', $sellerId->value())
            ->with(['category', 'images', 'bids'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    public function findByCategory(Id $categoryId, int $perPage = 15): LengthAwarePaginator
    {
        return Auction::where('category_id', $categoryId->value())
            ->where('status', 'active')
            ->with(['seller', 'category', 'images'])
            ->orderBy('end_time', 'asc')
            ->paginate($perPage);
    }

    public function findByStatus(AuctionStatus $status, int $perPage = 15): LengthAwarePaginator
    {
        return Auction::where('status', $status->value())
            ->with(['seller', 'category', 'images'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    public function findActive(int $perPage = 15): LengthAwarePaginator
    {
        return Auction::active()
            ->with(['seller', 'category', 'images'])
            ->orderBy('end_time', 'asc')
            ->paginate($perPage);
    }

    public function findEndingSoon(int $hours = 24, int $perPage = 15): LengthAwarePaginator
    {
        return Auction::endingSoon($hours)
            ->with(['seller', 'category', 'images'])
            ->orderBy('end_time', 'asc')
            ->paginate($perPage);
    }

    public function findFeatured(int $perPage = 15): LengthAwarePaginator
    {
        return Auction::featured()
            ->active()
            ->with(['seller', 'category', 'images'])
            ->orderBy('featured_until', 'desc')
            ->paginate($perPage);
    }

    public function findExpiredActive(): Collection
    {
        return Auction::where('status', 'active')
            ->where('end_time', '<=', now())
            ->get();
    }

    public function findScheduledToActivate(): Collection
    {
        return Auction::where('status', 'scheduled')
            ->where('start_time', '<=', now())
            ->get();
    }

    public function search(string $query, array $filters = [], int $perPage = 15): LengthAwarePaginator
    {
        $queryBuilder = Auction::query()
            ->where(function ($q) use ($query) {
                $q->where('title', 'ILIKE', "%{$query}%")
                  ->orWhere('description', 'ILIKE', "%{$query}%");
            });

        // Apply filters
        if (isset($filters['category_id'])) {
            $queryBuilder->where('category_id', $filters['category_id']);
        }

        if (isset($filters['status'])) {
            $queryBuilder->where('status', $filters['status']);
        }

        if (isset($filters['min_price'])) {
            $queryBuilder->where('current_bid', '>=', $filters['min_price']);
        }

        if (isset($filters['max_price'])) {
            $queryBuilder->where('current_bid', '<=', $filters['max_price']);
        }

        if (isset($filters['condition'])) {
            $queryBuilder->where('condition', $filters['condition']);
        }

        return $queryBuilder
            ->with(['seller', 'category', 'images'])
            ->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    public function findWatchedByUser(UserId $userId, int $perPage = 15): LengthAwarePaginator
    {
        return Auction::whereHas('watchers', function ($query) use ($userId) {
                $query->where('user_id', $userId->value());
            })
            ->with(['seller', 'category', 'images'])
            ->orderBy('end_time', 'asc')
            ->paginate($perPage);
    }

    public function findUserWatched(UserId $userId, int $perPage = 15): LengthAwarePaginator
    {
        return $this->findWatchedByUser($userId, $perPage);
    }

    public function findWonByUser(UserId $userId, int $perPage = 15): LengthAwarePaginator
    {
        return Auction::where('winner_id', $userId->value())
            ->where('status', 'ended')
            ->with(['seller', 'category', 'images'])
            ->orderBy('actual_end_time', 'desc')
            ->paginate($perPage);
    }

    public function findBidOnByUser(UserId $userId, int $perPage = 15): LengthAwarePaginator
    {
        return Auction::whereHas('bids', function ($query) use ($userId) {
                $query->where('user_id', $userId->value());
            })
            ->with(['seller', 'category', 'images'])
            ->orderBy('end_time', 'asc')
            ->paginate($perPage);
    }

    public function getStatistics(): array
    {
        return [
            'total_auctions' => Auction::count(),
            'active_auctions' => Auction::where('status', 'active')->count(),
            'ended_auctions' => Auction::where('status', 'ended')->count(),
            'total_bids' => Auction::sum('bids_count'),
            'total_value' => Auction::where('status', 'ended')->sum('final_price'),
            'average_final_price' => Auction::where('status', 'ended')->avg('final_price'),
        ];
    }

    public function countByStatus(AuctionStatus $status): int
    {
        return Auction::where('status', $status->value())->count();
    }

    public function findSimilar(EloquentAuction $auction, int $limit = 10): Collection
    {
        return Auction::where('category_id', $auction->category_id)
            ->where('id', '!=', $auction->id)
            ->where('status', 'active')
            ->whereBetween('current_bid', [
                $auction->current_bid * 0.5,
                $auction->current_bid * 2
            ])
            ->limit($limit)
            ->get();
    }

    public function findRelated(EloquentAuction $auction, int $limit = 10): Collection
    {
        return Auction::where('category_id', $auction->category_id)
            ->where('id', '!=', $auction->id)
            ->where('status', 'active')
            ->with(['seller', 'category', 'images'])
            ->orderBy('created_at', 'desc')
            ->limit($limit)
            ->get();
    }

    public function findRecentlyViewedByUser(UserId $userId, int $limit = 10): Collection
    {
        return Auction::whereHas('views', function ($query) use ($userId) {
                $query->where('user_id', $userId->value());
            })
            ->with(['seller', 'category', 'images'])
            ->orderBy('updated_at', 'desc')
            ->limit($limit)
            ->get();
    }

    public function incrementViewCount(Id $auctionId): void
    {
        Auction::where('id', $auctionId->value())->increment('views_count');
    }

    public function updateWatchersCount(Id $auctionId, int $count): void
    {
        Auction::where('id', $auctionId->value())->update(['watchers_count' => $count]);
    }

    public function updateBidsCount(Id $auctionId, int $count): void
    {
        Auction::where('id', $auctionId->value())->update(['bids_count' => $count]);
    }

    private function toDomainModel(Auction $auction): DomainAuction
    {
        // Convert Eloquent model to Domain model
        $id = Id::fromString((string) $auction->id);
        $sellerId = UserId::fromString((string) $auction->user_id);
        $categoryId = Id::fromString((string) $auction->category_id);

        $startingPrice = new Money($auction->starting_price, 'USD');
        $reservePrice = $auction->reserve_price
            ? ReservePrice::fromFloat($auction->reserve_price, 'USD')
            : ReservePrice::none();
        $buyoutPrice = $auction->buyout_price
            ? new Money($auction->buyout_price, 'USD')
            : null;

        $startTime = Timestamp::fromString($auction->start_time->toISOString());
        $endTime = Timestamp::fromString($auction->end_time->toISOString());
        $duration = new AuctionDuration($startTime, $endTime);

        // Create the domain auction using the constructor
        $domainAuction = new DomainAuction(
            $id,
            $sellerId,
            $categoryId,
            $auction->title,
            $auction->description,
            $startingPrice,
            $duration,
            $reservePrice,
            $buyoutPrice
        );

        // Set additional properties that aren't in the constructor
        $this->setDomainAuctionProperties($domainAuction, $auction);

        return $domainAuction;
    }

    private function toEloquentModel(DomainAuction $auction): Auction
    {
        // Convert Domain model to Eloquent model
        $eloquentAuction = new Auction();

        $eloquentAuction->id = $auction->id()->value();
        $eloquentAuction->user_id = $auction->sellerId()->value();
        $eloquentAuction->category_id = $auction->categoryId()->value();
        $eloquentAuction->title = $auction->title();
        $eloquentAuction->description = $auction->description();
        $eloquentAuction->condition = $auction->condition();
        $eloquentAuction->starting_price = $auction->startingPrice()->amount();
        $eloquentAuction->reserve_price = $auction->reservePrice()->amount();
        $eloquentAuction->current_bid = $auction->currentBid()->amount();
        $eloquentAuction->bid_increment = $auction->bidIncrement()->amount();
        $eloquentAuction->buyout_price = $auction->buyoutPrice()?->amount();
        $eloquentAuction->start_time = $auction->duration()->startTime()->value();
        $eloquentAuction->end_time = $auction->duration()->endTime()->value();
        $eloquentAuction->actual_end_time = $auction->actualEndTime()?->value();
        $eloquentAuction->status = $auction->status()->value();
        $eloquentAuction->auto_extend = $auction->autoExtend();
        $eloquentAuction->extend_minutes = $auction->extendMinutes();
        $eloquentAuction->views_count = $auction->viewsCount();
        $eloquentAuction->watchers_count = $auction->watchersCount();
        $eloquentAuction->bids_count = $auction->bidsCount();
        $eloquentAuction->winner_id = $auction->winnerId()?->value();
        $eloquentAuction->final_price = $auction->finalPrice()?->amount();
        $eloquentAuction->featured = $auction->featured();
        $eloquentAuction->featured_until = $auction->featuredUntil()?->value();
        $eloquentAuction->created_at = $auction->createdAt()->value();
        $eloquentAuction->updated_at = $auction->updatedAt()->value();

        return $eloquentAuction;
    }

    private function setDomainAuctionProperties(DomainAuction $domainAuction, Auction $auction): void
    {
        // Use reflection to set private properties that aren't available through setters
        $reflection = new \ReflectionClass($domainAuction);

        // Set current bid
        if ($auction->current_bid > 0) {
            $currentBidProperty = $reflection->getProperty('currentBid');
            $currentBidProperty->setAccessible(true);
            $currentBidProperty->setValue($domainAuction, BidAmount::fromFloat($auction->current_bid, 'USD'));
        }

        // Set status
        $statusProperty = $reflection->getProperty('status');
        $statusProperty->setAccessible(true);
        $statusProperty->setValue($domainAuction, new AuctionStatus($auction->status));

        // Set counts
        $viewsCountProperty = $reflection->getProperty('viewsCount');
        $viewsCountProperty->setAccessible(true);
        $viewsCountProperty->setValue($domainAuction, $auction->views_count);

        $watchersCountProperty = $reflection->getProperty('watchersCount');
        $watchersCountProperty->setAccessible(true);
        $watchersCountProperty->setValue($domainAuction, $auction->watchers_count);

        $bidsCountProperty = $reflection->getProperty('bidsCount');
        $bidsCountProperty->setAccessible(true);
        $bidsCountProperty->setValue($domainAuction, $auction->bids_count);

        // Set winner and final price if auction ended
        if ($auction->winner_id) {
            $winnerIdProperty = $reflection->getProperty('winnerId');
            $winnerIdProperty->setAccessible(true);
            $winnerIdProperty->setValue($domainAuction, UserId::fromString((string) $auction->winner_id));
        }

        if ($auction->final_price) {
            $finalPriceProperty = $reflection->getProperty('finalPrice');
            $finalPriceProperty->setAccessible(true);
            $finalPriceProperty->setValue($domainAuction, new Money($auction->final_price, 'USD'));
        }

        // Set featured properties
        $featuredProperty = $reflection->getProperty('featured');
        $featuredProperty->setAccessible(true);
        $featuredProperty->setValue($domainAuction, $auction->featured);

        if ($auction->featured_until) {
            $featuredUntilProperty = $reflection->getProperty('featuredUntil');
            $featuredUntilProperty->setAccessible(true);
            $featuredUntilProperty->setValue($domainAuction, Timestamp::fromString($auction->featured_until->toISOString()));
        }

        // Set timestamps
        $createdAtProperty = $reflection->getProperty('createdAt');
        $createdAtProperty->setAccessible(true);
        $createdAtProperty->setValue($domainAuction, Timestamp::fromString($auction->created_at->toISOString()));

        $updatedAtProperty = $reflection->getProperty('updatedAt');
        $updatedAtProperty->setAccessible(true);
        $updatedAtProperty->setValue($domainAuction, Timestamp::fromString($auction->updated_at->toISOString()));
    }
}

<?php

declare(strict_types=1);

namespace App\Infrastructure\Repositories;

use App\Domain\Auction\Models\Category as DomainCategory;
use App\Models\Category as EloquentCategory;
use App\Domain\Auction\Repositories\CategoryRepositoryInterface;
use App\Domain\Shared\ValueObjects\Id;
use App\Domain\Shared\ValueObjects\Slug;
use App\Models\Category;
use Illuminate\Support\Collection;

class EloquentCategoryRepository implements CategoryRepositoryInterface
{
    public function findById(Id $id): ?EloquentCategory
    {
        return Category::find($id->value());
    }

    public function findByIdOrFail(Id $id): EloquentCategory
    {
        return Category::findOrFail($id->value());
    }

    public function findBySlug(Slug $slug): ?EloquentCategory
    {
        return Category::where('slug', $slug->value())->first();
    }

    public function save(EloquentCategory $category): void
    {
        $category->save();
    }

    public function delete(EloquentCategory $category): void
    {
        $category->delete();
    }

    public function findAll(): Collection
    {
        return Category::orderBy('sort_order')
            ->orderBy('name')
            ->get();
    }

    public function findActive(): Collection
    {
        return Category::active()
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get();
    }

    public function findRootCategories(): Collection
    {
        return Category::root()
            ->active()
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get();
    }

    public function findChildCategories(Id $parentId): Collection
    {
        return Category::where('parent_id', $parentId->value())
            ->active()
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get();
    }

    public function findHierarchy(): Collection
    {
        return Category::with('children')
            ->root()
            ->active()
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get();
    }

    public function findAllWithHierarchy(): Collection
    {
        return Category::with(['children' => function ($query) {
                $query->where('is_active', true)
                      ->orderBy('sort_order')
                      ->orderBy('name');
            }])
            ->root()
            ->active()
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get();
    }

    public function findOrderedBySort(): Collection
    {
        return Category::active()
            ->orderBy('sort_order')
            ->orderBy('name')
            ->get();
    }

    public function findWithAuctionCounts(): Collection
    {
        return Category::withCount(['auctions', 'activeAuctions'])
            ->active()
            ->orderBy('sort_order')
            ->get();
    }

    public function findFeatured(int $limit = 10): Collection
    {
        return Category::where('is_featured', true)
            ->active()
            ->orderBy('sort_order')
            ->orderBy('name')
            ->limit($limit)
            ->get();
    }

    public function findPopular(int $limit = 10): Collection
    {
        return Category::withCount(['auctions as active_auctions_count' => function ($query) {
                $query->where('status', 'active');
            }])
            ->active()
            ->orderBy('active_auctions_count', 'desc')
            ->limit($limit)
            ->get();
    }

    public function searchByName(string $query): Collection
    {
        return Category::where('name', 'ILIKE', "%{$query}%")
            ->active()
            ->orderBy('name')
            ->get();
    }

    public function hasChildren(Id $categoryId): bool
    {
        return Category::where('parent_id', $categoryId->value())
            ->active()
            ->exists();
    }

    public function hasAuctions(Id $categoryId): bool
    {
        return Category::find($categoryId->value())
            ->auctions()
            ->exists();
    }

    public function countAuctions(Id $categoryId): int
    {
        return Category::find($categoryId->value())
            ->auctions()
            ->count();
    }

    public function countActiveAuctions(Id $categoryId): int
    {
        return Category::find($categoryId->value())
            ->activeAuctions()
            ->count();
    }

    public function findCategoryPath(Id $categoryId): Collection
    {
        $path = collect();
        $category = Category::find($categoryId->value());

        while ($category) {
            $path->prepend($category);
            $category = $category->parent;
        }

        return $path;
    }

    public function slugExists(Slug $slug, ?Id $excludeId = null): bool
    {
        $query = Category::where('slug', $slug->value());

        if ($excludeId) {
            $query->where('id', '!=', $excludeId->value());
        }

        return $query->exists();
    }

    public function updateSortOrders(array $categoryOrders): void
    {
        foreach ($categoryOrders as $categoryId => $sortOrder) {
            Category::where('id', $categoryId)
                ->update(['sort_order' => $sortOrder]);
        }
    }

    private function toDomainModel(Category $category): DomainCategory
    {
        // Convert Eloquent model to Domain model
        // This is a placeholder - implement based on your domain model constructor
        throw new \Exception('Domain model conversion not implemented yet');
    }

    private function toEloquentModel(DomainCategory $category): Category
    {
        // Convert Domain model to Eloquent model
        // This is a placeholder - implement based on your domain model structure
        throw new \Exception('Eloquent model conversion not implemented yet');
    }
}

<?php

declare(strict_types=1);

namespace Tests\Unit\Domain\Auction;

use App\Domain\Auction\Models\Auction;
use App\Domain\Auction\ValueObjects\AuctionDuration;
use App\Domain\Auction\ValueObjects\AuctionStatus;
use App\Domain\Auction\ValueObjects\ReservePrice;
use App\Domain\Shared\ValueObjects\Id;
use App\Domain\Shared\ValueObjects\Money;
use App\Domain\Shared\ValueObjects\Timestamp;
use App\Domain\User\ValueObjects\UserId;
use PHPUnit\Framework\TestCase;

class AuctionTest extends TestCase
{
    private function createTestAuction(): Auction
    {
        $id = Id::generate();
        $sellerId = UserId::generate();
        $categoryId = Id::generate();
        $startingPrice = new Money(10000, 'USD'); // $100.00
        $startTime = Timestamp::now();
        $endTime = Timestamp::fromString($startTime->value()->addDays(7)->toISOString());
        $duration = new AuctionDuration($startTime, $endTime);
        $reservePrice = ReservePrice::fromFloat(150.00, 'USD');

        return new Auction(
            $id,
            $sellerId,
            $categoryId,
            'Test Auction',
            'A test auction description',
            $startingPrice,
            $duration,
            $reservePrice
        );
    }

    public function test_auction_can_be_created(): void
    {
        $auction = $this->createTestAuction();

        $this->assertInstanceOf(Auction::class, $auction);
        $this->assertEquals('Test Auction', $auction->title());
        $this->assertEquals('A test auction description', $auction->description());
        $this->assertEquals(10000, $auction->startingPrice()->amount());
        $this->assertEquals('USD', $auction->startingPrice()->currency());
    }

    public function test_auction_starts_with_scheduled_status(): void
    {
        $auction = $this->createTestAuction();

        $this->assertEquals('scheduled', $auction->status()->value());
        $this->assertFalse($auction->isActive());
        $this->assertFalse($auction->hasEnded());
    }

    public function test_auction_can_be_started(): void
    {
        $auction = $this->createTestAuction();

        $auction->start();

        $this->assertEquals('active', $auction->status()->value());
        $this->assertTrue($auction->isActive());
        $this->assertFalse($auction->hasEnded());
    }

    public function test_auction_can_be_ended(): void
    {
        $auction = $this->createTestAuction();
        $auction->start();

        $auction->end();

        $this->assertEquals('ended', $auction->status()->value());
        $this->assertFalse($auction->isActive());
        $this->assertTrue($auction->hasEnded());
    }

    public function test_auction_cannot_be_started_twice(): void
    {
        $auction = $this->createTestAuction();
        $auction->start();

        $this->expectException(\DomainException::class);
        $this->expectExceptionMessage('Auction is already active');

        $auction->start();
    }

    public function test_auction_cannot_be_ended_if_not_active(): void
    {
        $auction = $this->createTestAuction();

        $this->expectException(\DomainException::class);
        $this->expectExceptionMessage('Auction is not active');

        $auction->end();
    }

    public function test_auction_current_bid_starts_at_starting_price(): void
    {
        $auction = $this->createTestAuction();

        $this->assertEquals(10000, $auction->currentBid()->amount());
        $this->assertEquals('USD', $auction->currentBid()->currency());
    }

    public function test_auction_can_update_current_bid(): void
    {
        $auction = $this->createTestAuction();
        $newBid = new Money(12000, 'USD'); // $120.00

        $auction->updateCurrentBid($newBid);

        $this->assertEquals(12000, $auction->currentBid()->amount());
    }

    public function test_auction_cannot_update_bid_to_lower_amount(): void
    {
        $auction = $this->createTestAuction();
        $auction->updateCurrentBid(new Money(12000, 'USD'));
        $lowerBid = new Money(11000, 'USD');

        $this->expectException(\DomainException::class);
        $this->expectExceptionMessage('New bid must be higher than current bid');

        $auction->updateCurrentBid($lowerBid);
    }

    public function test_auction_can_increment_bid_count(): void
    {
        $auction = $this->createTestAuction();

        $this->assertEquals(0, $auction->bidsCount());

        $auction->incrementBidCount();

        $this->assertEquals(1, $auction->bidsCount());
    }

    public function test_auction_can_increment_view_count(): void
    {
        $auction = $this->createTestAuction();

        $this->assertEquals(0, $auction->viewsCount());

        $auction->incrementViewCount();

        $this->assertEquals(1, $auction->viewsCount());
    }

    public function test_auction_can_add_watcher(): void
    {
        $auction = $this->createTestAuction();

        $this->assertEquals(0, $auction->watchersCount());

        $auction->addWatcher();

        $this->assertEquals(1, $auction->watchersCount());
    }

    public function test_auction_can_remove_watcher(): void
    {
        $auction = $this->createTestAuction();
        $auction->addWatcher();
        $auction->addWatcher();

        $this->assertEquals(2, $auction->watchersCount());

        $auction->removeWatcher();

        $this->assertEquals(1, $auction->watchersCount());
    }

    public function test_auction_watcher_count_cannot_go_below_zero(): void
    {
        $auction = $this->createTestAuction();

        $this->assertEquals(0, $auction->watchersCount());

        $auction->removeWatcher();

        $this->assertEquals(0, $auction->watchersCount());
    }

    public function test_auction_can_be_featured(): void
    {
        $auction = $this->createTestAuction();
        $featuredUntil = Timestamp::fromString(now()->addDays(7)->toISOString());

        $this->assertFalse($auction->featured());

        $auction->feature($featuredUntil);

        $this->assertTrue($auction->featured());
        $this->assertEquals($featuredUntil->value(), $auction->featuredUntil()->value());
    }

    public function test_auction_can_be_unfeatured(): void
    {
        $auction = $this->createTestAuction();
        $featuredUntil = Timestamp::fromString(now()->addDays(7)->toISOString());
        $auction->feature($featuredUntil);

        $this->assertTrue($auction->featured());

        $auction->unfeature();

        $this->assertFalse($auction->featured());
        $this->assertNull($auction->featuredUntil());
    }

    public function test_auction_can_set_winner(): void
    {
        $auction = $this->createTestAuction();
        $winnerId = UserId::generate();
        $finalPrice = new Money(15000, 'USD');

        $auction->setWinner($winnerId, $finalPrice);

        $this->assertEquals($winnerId->value(), $auction->winnerId()->value());
        $this->assertEquals(15000, $auction->finalPrice()->amount());
    }

    public function test_auction_can_check_if_reserve_met(): void
    {
        $auction = $this->createTestAuction();

        // Current bid is $100, reserve is $150
        $this->assertFalse($auction->isReserveMet());

        // Update bid to $160
        $auction->updateCurrentBid(new Money(16000, 'USD'));

        $this->assertTrue($auction->isReserveMet());
    }

    public function test_auction_with_no_reserve_always_meets_reserve(): void
    {
        $id = Id::generate();
        $sellerId = UserId::generate();
        $categoryId = Id::generate();
        $startingPrice = new Money(10000, 'USD');
        $startTime = Timestamp::now();
        $endTime = Timestamp::fromString($startTime->value()->addDays(7)->toISOString());
        $duration = new AuctionDuration($startTime, $endTime);
        $reservePrice = ReservePrice::none();

        $auction = new Auction(
            $id,
            $sellerId,
            $categoryId,
            'No Reserve Auction',
            'An auction with no reserve',
            $startingPrice,
            $duration,
            $reservePrice
        );

        $this->assertTrue($auction->isReserveMet());
    }

    public function test_auction_can_extend_end_time(): void
    {
        $auction = $this->createTestAuction();
        $originalEndTime = $auction->duration()->endTime();
        $extensionMinutes = 10;

        $auction->extendEndTime($extensionMinutes);

        $newEndTime = $auction->duration()->endTime();
        $expectedEndTime = $originalEndTime->value()->addMinutes($extensionMinutes);

        $this->assertEquals(
            $expectedEndTime->toISOString(),
            $newEndTime->value()->toISOString()
        );
    }

    public function test_auction_validates_title_length(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Title must be between 3 and 255 characters');

        $id = Id::generate();
        $sellerId = UserId::generate();
        $categoryId = Id::generate();
        $startingPrice = new Money(10000, 'USD');
        $startTime = Timestamp::now();
        $endTime = Timestamp::fromString($startTime->value()->addDays(7)->toISOString());
        $duration = new AuctionDuration($startTime, $endTime);
        $reservePrice = ReservePrice::none();

        new Auction(
            $id,
            $sellerId,
            $categoryId,
            'AB', // Too short
            'Valid description',
            $startingPrice,
            $duration,
            $reservePrice
        );
    }

    public function test_auction_validates_description_length(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Description must be between 10 and 5000 characters');

        $id = Id::generate();
        $sellerId = UserId::generate();
        $categoryId = Id::generate();
        $startingPrice = new Money(10000, 'USD');
        $startTime = Timestamp::now();
        $endTime = Timestamp::fromString($startTime->value()->addDays(7)->toISOString());
        $duration = new AuctionDuration($startTime, $endTime);
        $reservePrice = ReservePrice::none();

        new Auction(
            $id,
            $sellerId,
            $categoryId,
            'Valid Title',
            'Short', // Too short
            $startingPrice,
            $duration,
            $reservePrice
        );
    }
}

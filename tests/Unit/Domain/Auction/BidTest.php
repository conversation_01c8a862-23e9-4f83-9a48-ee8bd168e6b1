<?php

declare(strict_types=1);

namespace Tests\Unit\Domain\Auction;

use App\Domain\Auction\Models\Bid;
use App\Domain\Auction\ValueObjects\BidAmount;
use App\Domain\Shared\ValueObjects\Id;
use App\Domain\Shared\ValueObjects\Money;
use App\Domain\User\ValueObjects\UserId;
use PHPUnit\Framework\TestCase;

class BidTest extends TestCase
{
    private function createTestBid(): Bid
    {
        $id = Id::generate();
        $auctionId = Id::generate();
        $bidderId = UserId::generate();
        $amount = BidAmount::fromFloat(100.00, 'USD');

        return new Bid(
            $id,
            $auctionId,
            $bidderId,
            $amount,
            'regular',
            null,
            '***********',
            'Mozilla/5.0 Test Browser'
        );
    }

    public function test_bid_can_be_created(): void
    {
        $bid = $this->createTestBid();

        $this->assertInstanceOf(Bid::class, $bid);
        $this->assertEquals(10000, $bid->amount()->amount()); // $100.00 in cents
        $this->assertEquals('USD', $bid->amount()->currency());
        $this->assertEquals('regular', $bid->type());
        $this->assertTrue($bid->isValid());
        $this->assertFalse($bid->isWinning());
    }

    public function test_proxy_bid_can_be_created_with_max_bid(): void
    {
        $id = Id::generate();
        $auctionId = Id::generate();
        $bidderId = UserId::generate();
        $amount = BidAmount::fromFloat(100.00, 'USD');
        $maxBid = new Money(20000, 'USD'); // $200.00

        $bid = new Bid(
            $id,
            $auctionId,
            $bidderId,
            $amount,
            'proxy',
            $maxBid,
            '***********',
            'Mozilla/5.0 Test Browser'
        );

        $this->assertEquals('proxy', $bid->type());
        $this->assertEquals(20000, $bid->maxBid()->amount());
    }

    public function test_bid_can_be_marked_as_winning(): void
    {
        $bid = $this->createTestBid();

        $this->assertFalse($bid->isWinning());

        $bid->markAsWinning();

        $this->assertTrue($bid->isWinning());
    }

    public function test_bid_can_be_marked_as_not_winning(): void
    {
        $bid = $this->createTestBid();
        $bid->markAsWinning();

        $this->assertTrue($bid->isWinning());

        $bid->markAsNotWinning();

        $this->assertFalse($bid->isWinning());
    }

    public function test_bid_can_be_invalidated(): void
    {
        $bid = $this->createTestBid();

        $this->assertTrue($bid->isValid());
        $this->assertNull($bid->invalidationReason());

        $bid->invalidate('Suspicious bidding pattern');

        $this->assertFalse($bid->isValid());
        $this->assertEquals('Suspicious bidding pattern', $bid->invalidationReason());
        $this->assertNotNull($bid->invalidatedAt());
    }

    public function test_bid_can_be_validated_after_invalidation(): void
    {
        $bid = $this->createTestBid();
        $bid->invalidate('Test reason');

        $this->assertFalse($bid->isValid());

        $bid->validate();

        $this->assertTrue($bid->isValid());
        $this->assertNull($bid->invalidationReason());
        $this->assertNull($bid->invalidatedAt());
    }

    public function test_bid_validates_amount_is_positive(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Bid amount must be positive');

        $id = Id::generate();
        $auctionId = Id::generate();
        $bidderId = UserId::generate();
        $amount = BidAmount::fromFloat(-10.00, 'USD'); // Negative amount

        new Bid(
            $id,
            $auctionId,
            $bidderId,
            $amount,
            'regular',
            null,
            '***********',
            'Mozilla/5.0 Test Browser'
        );
    }

    public function test_bid_validates_bid_type(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid bid type');

        $id = Id::generate();
        $auctionId = Id::generate();
        $bidderId = UserId::generate();
        $amount = BidAmount::fromFloat(100.00, 'USD');

        new Bid(
            $id,
            $auctionId,
            $bidderId,
            $amount,
            'invalid_type', // Invalid type
            null,
            '***********',
            'Mozilla/5.0 Test Browser'
        );
    }

    public function test_proxy_bid_validates_max_bid_is_higher_than_amount(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Max bid must be higher than bid amount');

        $id = Id::generate();
        $auctionId = Id::generate();
        $bidderId = UserId::generate();
        $amount = BidAmount::fromFloat(100.00, 'USD');
        $maxBid = new Money(5000, 'USD'); // $50.00 - lower than amount

        new Bid(
            $id,
            $auctionId,
            $bidderId,
            $amount,
            'proxy',
            $maxBid,
            '***********',
            'Mozilla/5.0 Test Browser'
        );
    }

    public function test_regular_bid_cannot_have_max_bid(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Regular bids cannot have a max bid amount');

        $id = Id::generate();
        $auctionId = Id::generate();
        $bidderId = UserId::generate();
        $amount = BidAmount::fromFloat(100.00, 'USD');
        $maxBid = new Money(20000, 'USD');

        new Bid(
            $id,
            $auctionId,
            $bidderId,
            $amount,
            'regular',
            $maxBid, // Should be null for regular bids
            '***********',
            'Mozilla/5.0 Test Browser'
        );
    }

    public function test_proxy_bid_must_have_max_bid(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Proxy bids must have a max bid amount');

        $id = Id::generate();
        $auctionId = Id::generate();
        $bidderId = UserId::generate();
        $amount = BidAmount::fromFloat(100.00, 'USD');

        new Bid(
            $id,
            $auctionId,
            $bidderId,
            $amount,
            'proxy',
            null, // Should not be null for proxy bids
            '***********',
            'Mozilla/5.0 Test Browser'
        );
    }

    public function test_bid_validates_ip_address_format(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid IP address format');

        $id = Id::generate();
        $auctionId = Id::generate();
        $bidderId = UserId::generate();
        $amount = BidAmount::fromFloat(100.00, 'USD');

        new Bid(
            $id,
            $auctionId,
            $bidderId,
            $amount,
            'regular',
            null,
            'invalid-ip', // Invalid IP format
            'Mozilla/5.0 Test Browser'
        );
    }

    public function test_bid_accepts_valid_ipv4_address(): void
    {
        $id = Id::generate();
        $auctionId = Id::generate();
        $bidderId = UserId::generate();
        $amount = BidAmount::fromFloat(100.00, 'USD');

        $bid = new Bid(
            $id,
            $auctionId,
            $bidderId,
            $amount,
            'regular',
            null,
            '***********00',
            'Mozilla/5.0 Test Browser'
        );

        $this->assertEquals('***********00', $bid->ipAddress());
    }

    public function test_bid_accepts_valid_ipv6_address(): void
    {
        $id = Id::generate();
        $auctionId = Id::generate();
        $bidderId = UserId::generate();
        $amount = BidAmount::fromFloat(100.00, 'USD');

        $bid = new Bid(
            $id,
            $auctionId,
            $bidderId,
            $amount,
            'regular',
            null,
            '2001:0db8:85a3:0000:0000:8a2e:0370:7334',
            'Mozilla/5.0 Test Browser'
        );

        $this->assertEquals('2001:0db8:85a3:0000:0000:8a2e:0370:7334', $bid->ipAddress());
    }

    public function test_bid_validates_user_agent_length(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('User agent must not exceed 500 characters');

        $id = Id::generate();
        $auctionId = Id::generate();
        $bidderId = UserId::generate();
        $amount = BidAmount::fromFloat(100.00, 'USD');
        $longUserAgent = str_repeat('A', 501); // 501 characters

        new Bid(
            $id,
            $auctionId,
            $bidderId,
            $amount,
            'regular',
            null,
            '***********',
            $longUserAgent
        );
    }

    public function test_bid_can_be_compared_by_amount(): void
    {
        $bid1 = $this->createTestBid();
        
        $id2 = Id::generate();
        $auctionId2 = Id::generate();
        $bidderId2 = UserId::generate();
        $amount2 = BidAmount::fromFloat(150.00, 'USD');

        $bid2 = new Bid(
            $id2,
            $auctionId2,
            $bidderId2,
            $amount2,
            'regular',
            null,
            '192.168.1.2',
            'Mozilla/5.0 Test Browser'
        );

        $this->assertTrue($bid2->amount()->amount() > $bid1->amount()->amount());
    }
}

<?php

use App\Models\Auction;
use App\Models\Category;

test('debug auction show route', function () {
    $category = Category::factory()->create();
    $auction = Auction::factory()->create([
        'category_id' => $category->id,
    ]);

    echo "Testing auction show route: /auctions/{$auction->id}\n";
    echo "Auction ID: {$auction->id}\n";
    echo "Category ID: {$auction->category_id}\n";

    try {
        // Test the repository method directly
        $auctionRepo = app(\App\Domain\Auction\Repositories\AuctionRepositoryInterface::class);
        $foundAuction = $auctionRepo->findByIdOrFail(\App\Domain\Shared\ValueObjects\Id::fromString((string) $auction->id));
        echo "Repository findByIdOrFail: SUCCESS\n";

        // Test incrementViewCount
        $auctionRepo->incrementViewCount(\App\Domain\Shared\ValueObjects\Id::fromString((string) $auction->id));
        echo "Repository incrementViewCount: SUCCESS\n";

        // Test findRelated
        $related = $auctionRepo->findRelated($foundAuction, 4);
        echo "Repository findRelated: SUCCESS (found " . $related->count() . " related)\n";

        // Test the actual route
        $response = $this->get("/auctions/{$auction->id}");
        echo "Route Status: {$response->status()}\n";

        if ($response->status() >= 400) {
            echo "Error Content: " . substr($response->getContent(), 0, 1000) . "\n";
        }

    } catch (Exception $e) {
        echo "Exception: " . $e->getMessage() . "\n";
        echo "File: " . $e->getFile() . ":" . $e->getLine() . "\n";
        echo "Trace: " . $e->getTraceAsString() . "\n";
    }
});

<?php

use App\Models\User;
use App\Models\Auction;
use App\Models\Category;
use App\Models\Bid;

describe('Comprehensive Error Report Tests', function () {
    
    test('generate comprehensive error report for all routes', function () {
        $errors = [];
        $user = createTestUser();
        $category = createTestCategory();
        $auction = createTestAuction([
            'user_id' => $user->id,
            'category_id' => $category->id,
        ]);

        // Test all public routes with non-existent resources
        $publicRoutes = [
            '/auctions/99999' => 'GET',
            '/categories/non-existent' => 'GET',
            '/categories/non-existent/auctions' => 'GET',
            '/auctions/99999/highest-bid' => 'GET',
            '/auctions/99999/recent-bids' => 'GET',
            '/auctions/99999/winning-bid' => 'GET',
        ];

        foreach ($publicRoutes as $route => $method) {
            $response = $this->call($method, $route);
            $status = $response->status();
            
            if ($status === 500) {
                $errors[] = [
                    'route' => $route,
                    'method' => $method,
                    'expected' => '404',
                    'actual' => '500',
                    'type' => 'SERVER_ERROR',
                    'auth_required' => false,
                ];
            } elseif ($status !== 404 && $status !== 302) {
                $errors[] = [
                    'route' => $route,
                    'method' => $method,
                    'expected' => '404',
                    'actual' => (string)$status,
                    'type' => 'UNEXPECTED_STATUS',
                    'auth_required' => false,
                ];
            }
        }

        // Test authenticated routes with non-existent resources
        $this->actingAs($user);
        
        $authRoutes = [
            '/auctions/99999/edit' => 'GET',
            '/auctions/99999' => 'PUT',
            '/auctions/99999' => 'DELETE',
            '/auctions/99999/activate' => 'POST',
            '/auctions/99999/end' => 'POST',
            '/auctions/99999/images' => 'POST',
            '/bids/99999' => 'DELETE',
            '/payments/99999' => 'GET',
            '/payments/create/99999' => 'GET',
            '/watchlist/check/99999' => 'GET',
            '/watchlist/toggle/99999' => 'POST',
            '/watchlist/99999' => 'DELETE',
        ];

        foreach ($authRoutes as $route => $method) {
            $response = $this->call($method, $route, []);
            $status = $response->status();
            
            if ($status === 500) {
                $errors[] = [
                    'route' => $route,
                    'method' => $method,
                    'expected' => '404',
                    'actual' => '500',
                    'type' => 'SERVER_ERROR',
                    'auth_required' => true,
                ];
            } elseif (!in_array($status, [404, 302, 422])) {
                $errors[] = [
                    'route' => $route,
                    'method' => $method,
                    'expected' => '404 or 302 or 422',
                    'actual' => (string)$status,
                    'type' => 'UNEXPECTED_STATUS',
                    'auth_required' => true,
                ];
            }
        }

        // Test API routes
        $apiRoutes = [
            '/api/auctions/99999' => 'GET',
            '/api/categories/99999' => 'GET',
            '/api/categories/99999/auctions' => 'GET',
            '/api/bids/99999' => 'GET',
            '/api/auctions/99999/bids' => 'GET',
            '/api/auctions/99999/statistics' => 'GET',
            '/api/auctions/99999/highest-bid' => 'GET',
            '/api/auctions/99999/winning-bid' => 'GET',
            '/api/auctions/99999/recent-bids' => 'GET',
        ];

        foreach ($apiRoutes as $route => $method) {
            $response = $this->json($method, $route);
            $status = $response->status();
            
            if ($status === 500) {
                $errors[] = [
                    'route' => $route,
                    'method' => $method,
                    'expected' => '404',
                    'actual' => '500',
                    'type' => 'API_SERVER_ERROR',
                    'auth_required' => false,
                ];
            } elseif ($status !== 404) {
                $errors[] = [
                    'route' => $route,
                    'method' => $method,
                    'expected' => '404',
                    'actual' => (string)$status,
                    'type' => 'API_UNEXPECTED_STATUS',
                    'auth_required' => false,
                ];
            }
        }

        // Generate report
        if (!empty($errors)) {
            $report = "\n\n=== ERROR DETECTION REPORT ===\n";
            $report .= "Found " . count($errors) . " potential issues:\n\n";
            
            $serverErrors = array_filter($errors, fn($e) => str_contains($e['type'], 'SERVER_ERROR'));
            $unexpectedStatus = array_filter($errors, fn($e) => str_contains($e['type'], 'UNEXPECTED_STATUS'));
            
            if (!empty($serverErrors)) {
                $report .= "🚨 SERVER ERRORS (500) - These need immediate attention:\n";
                foreach ($serverErrors as $error) {
                    $report .= "  - {$error['method']} {$error['route']} (Auth: " . ($error['auth_required'] ? 'Yes' : 'No') . ")\n";
                }
                $report .= "\n";
            }
            
            if (!empty($unexpectedStatus)) {
                $report .= "⚠️  UNEXPECTED STATUS CODES:\n";
                foreach ($unexpectedStatus as $error) {
                    $report .= "  - {$error['method']} {$error['route']} - Expected: {$error['expected']}, Got: {$error['actual']}\n";
                }
                $report .= "\n";
            }
            
            $report .= "=== END REPORT ===\n\n";
            
            // Output the report
            fwrite(STDERR, $report);
        }

        // The test passes regardless, but we've generated a useful report
        expect(true)->toBeTrue();
    });

    test('test malformed input handling', function () {
        $errors = [];
        $user = createTestUser();
        $this->actingAs($user);

        // Test malformed IDs
        $malformedIds = ['abc', '12.5', '-1', '0', 'null', 'undefined', '../../etc/passwd'];
        
        foreach ($malformedIds as $id) {
            $routes = [
                "/auctions/{$id}" => 'GET',
                "/auctions/{$id}/edit" => 'GET',
                "/bids/{$id}" => 'DELETE',
                "/payments/{$id}" => 'GET',
            ];
            
            foreach ($routes as $route => $method) {
                try {
                    $response = $this->call($method, $route);
                    $status = $response->status();
                    
                    if ($status === 500) {
                        $errors[] = [
                            'route' => $route,
                            'method' => $method,
                            'input' => $id,
                            'status' => 500,
                            'type' => 'MALFORMED_INPUT_500',
                        ];
                    }
                } catch (\Exception $e) {
                    $errors[] = [
                        'route' => $route,
                        'method' => $method,
                        'input' => $id,
                        'exception' => get_class($e),
                        'type' => 'MALFORMED_INPUT_EXCEPTION',
                    ];
                }
            }
        }

        if (!empty($errors)) {
            $report = "\n\n=== MALFORMED INPUT REPORT ===\n";
            $report .= "Found " . count($errors) . " issues with malformed input handling:\n\n";
            
            foreach ($errors as $error) {
                if ($error['type'] === 'MALFORMED_INPUT_500') {
                    $report .= "🚨 500 Error: {$error['method']} {$error['route']} with input '{$error['input']}'\n";
                } else {
                    $report .= "💥 Exception: {$error['method']} {$error['route']} with input '{$error['input']}' - {$error['exception']}\n";
                }
            }
            
            $report .= "\n=== END MALFORMED INPUT REPORT ===\n\n";
            fwrite(STDERR, $report);
        }

        expect(true)->toBeTrue();
    });

    test('test authentication bypass attempts', function () {
        $errors = [];
        
        // Test accessing protected routes without authentication
        $protectedRoutes = [
            '/dashboard' => 'GET',
            '/auctions/create' => 'GET',
            '/my/auctions' => 'GET',
            '/my/bids' => 'GET',
            '/my/profile' => 'GET',
            '/watchlist' => 'GET',
            '/payments' => 'GET',
            '/auctions' => 'POST',
            '/bids' => 'POST',
            '/watchlist' => 'POST',
        ];

        foreach ($protectedRoutes as $route => $method) {
            $response = $this->call($method, $route, []);
            $status = $response->status();
            
            if ($status === 200) {
                $errors[] = [
                    'route' => $route,
                    'method' => $method,
                    'status' => 200,
                    'type' => 'AUTH_BYPASS',
                ];
            } elseif ($status === 500) {
                $errors[] = [
                    'route' => $route,
                    'method' => $method,
                    'status' => 500,
                    'type' => 'AUTH_SERVER_ERROR',
                ];
            }
        }

        if (!empty($errors)) {
            $report = "\n\n=== AUTHENTICATION SECURITY REPORT ===\n";
            $report .= "Found " . count($errors) . " authentication-related issues:\n\n";
            
            foreach ($errors as $error) {
                if ($error['type'] === 'AUTH_BYPASS') {
                    $report .= "🔓 SECURITY ISSUE: {$error['method']} {$error['route']} accessible without authentication!\n";
                } else {
                    $report .= "🚨 Server Error: {$error['method']} {$error['route']} returned 500 when checking auth\n";
                }
            }
            
            $report .= "\n=== END AUTHENTICATION REPORT ===\n\n";
            fwrite(STDERR, $report);
        }

        expect(true)->toBeTrue();
    });
});

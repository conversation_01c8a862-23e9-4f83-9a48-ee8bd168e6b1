<?php

use App\Models\Setting;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Seed default settings
    $this->artisan('db:seed', ['--class' => 'SettingsSeeder']);
});

test('admin can access settings page', function () {
    $admin = User::factory()->create([
        'role' => 'admin',
        'is_active' => true,
        'email_verified_at' => now(),
    ]);
    
    $this->actingAs($admin);
    
    $response = $this->get('/admin/settings');
    expect($response->status())->toBe(200);
});

test('regular user cannot access admin settings page', function () {
    $user = User::factory()->create([
        'role' => 'user',
        'is_active' => true,
        'email_verified_at' => now(),
    ]);
    
    $this->actingAs($user);
    
    $response = $this->get('/admin/settings');
    expect($response->status())->toBe(403);
});

test('guest cannot access admin settings page', function () {
    $response = $this->get('/admin/settings');
    expect($response->status())->toBe(302); // Redirect to login
});

test('admin can update settings', function () {
    $admin = User::factory()->create([
        'role' => 'admin',
        'is_active' => true,
        'email_verified_at' => now(),
    ]);
    
    $this->actingAs($admin);
    
    $response = $this->put('/admin/settings', [
        'settings' => [
            [
                'key' => 'allow_user_auction_creation',
                'value' => false,
                'type' => 'boolean',
            ],
            [
                'key' => 'site_name',
                'value' => 'Updated Auction Site',
                'type' => 'string',
            ],
        ],
    ]);
    
    expect($response->status())->toBe(302); // Redirect after update
    
    // Verify settings were updated
    expect(Setting::get('allow_user_auction_creation'))->toBeFalse();
    expect(Setting::get('site_name'))->toBe('Updated Auction Site');
});

test('regular user cannot update settings', function () {
    $user = User::factory()->create([
        'role' => 'user',
        'is_active' => true,
        'email_verified_at' => now(),
    ]);
    
    $this->actingAs($user);
    
    $response = $this->put('/admin/settings', [
        'settings' => [
            [
                'key' => 'allow_user_auction_creation',
                'value' => false,
                'type' => 'boolean',
            ],
        ],
    ]);
    
    expect($response->status())->toBe(403);
});

test('admin can access admin dashboard', function () {
    $admin = User::factory()->create([
        'role' => 'admin',
        'is_active' => true,
        'email_verified_at' => now(),
    ]);
    
    $this->actingAs($admin);
    
    $response = $this->get('/admin');
    expect($response->status())->toBe(200);
});

test('admin dashboard shows system statistics', function () {
    $admin = User::factory()->create([
        'role' => 'admin',
        'is_active' => true,
        'email_verified_at' => now(),
    ]);
    
    $this->actingAs($admin);
    
    $response = $this->get('/admin');
    
    expect($response->status())->toBe(200);
    
    // Check that the response contains expected data structure
    $response->assertInertia(fn ($page) => 
        $page->component('Admin/Dashboard')
             ->has('stats')
             ->has('stats.users')
             ->has('stats.auctions')
             ->has('stats.bids')
             ->has('systemSettings')
    );
});

test('setting model caches values correctly', function () {
    // Set a value
    Setting::set('test_cache_key', 'test_value', 'string');
    
    // Retrieve it (should be cached)
    $value1 = Setting::get('test_cache_key');
    expect($value1)->toBe('test_value');
    
    // Update the database directly (bypassing the model)
    \DB::table('settings')
        ->where('key', 'test_cache_key')
        ->update(['value' => 'updated_value']);
    
    // Should still return cached value
    $value2 = Setting::get('test_cache_key');
    expect($value2)->toBe('test_value');
    
    // Clear cache and check again
    Setting::clearCache();
    $value3 = Setting::get('test_cache_key');
    expect($value3)->toBe('updated_value');
});

test('setting model handles different data types correctly', function () {
    // Boolean
    Setting::set('bool_setting', true, 'boolean');
    expect(Setting::get('bool_setting'))->toBeTrue();
    
    Setting::set('bool_setting', false, 'boolean');
    expect(Setting::get('bool_setting'))->toBeFalse();
    
    // Integer
    Setting::set('int_setting', 42, 'integer');
    expect(Setting::get('int_setting'))->toBe(42);
    
    // Float
    Setting::set('float_setting', 3.14, 'float');
    expect(Setting::get('float_setting'))->toBe(3.14);
    
    // JSON
    $jsonData = ['key' => 'value', 'number' => 123];
    Setting::set('json_setting', $jsonData, 'json');
    expect(Setting::get('json_setting'))->toBe($jsonData);
    
    // String
    Setting::set('string_setting', 'hello world', 'string');
    expect(Setting::get('string_setting'))->toBe('hello world');
});

test('public settings are shared with frontend', function () {
    $admin = User::factory()->create([
        'role' => 'admin',
        'is_active' => true,
        'email_verified_at' => now(),
    ]);
    
    $this->actingAs($admin);
    
    $response = $this->get('/');
    
    // Check that public settings are included in the Inertia response
    $response->assertInertia(fn ($page) => 
        $page->has('settings')
             ->has('settings.allow_user_auction_creation')
             ->has('settings.site_name')
    );
});

<?php

use App\Models\User;
use App\Models\Auction;
use App\Models\Category;
use App\Models\Bid;

describe('404 Error Detection Tests', function () {

    test('non-existent auction returns 404', function () {
        $response = $this->get('/auctions/99999');
        expect($response->status())->toBeNotFound();
    });

    test('non-existent category returns 404', function () {
        $response = $this->get('/categories/non-existent-category');
        expect($response->status())->toBeNotFound();
    });

    test('non-existent category auctions returns 404', function () {
        $response = $this->get('/categories/non-existent-category/auctions');
        expect($response->status())->toBeNotFound();
    });

    test('non-existent user profile returns 404', function () {
        $user = createTestUser();
        $this->actingAs($user);

        $response = $this->get('/users/99999/profile');
        expect($response->status())->toBeNotFound();
    });

    test('non-existent payment returns 404', function () {
        $user = createTestUser();
        $this->actingAs($user);

        $response = $this->get('/payments/99999');
        expect($response->status())->toBeNotFound();
    });

    test('accessing non-existent auction edit page returns 404', function () {
        $user = createTestUser();
        $this->actingAs($user);

        $response = $this->get('/auctions/99999/edit');
        expect($response->status())->toBeNotFound();
    });

    test('accessing auction that belongs to another user for editing returns 404', function () {
        $owner = createTestUser();
        $otherUser = createTestUser();
        $category = createTestCategory();

        $auction = createTestAuction([
            'user_id' => $owner->id,
            'category_id' => $category->id,
        ]);

        $this->actingAs($otherUser);
        $response = $this->get("/auctions/{$auction->id}/edit");
        expect($response->status())->toBeNotFound();
    });

    test('non-existent bid returns appropriate error', function () {
        $user = createTestUser();
        $this->actingAs($user);

        $response = $this->delete('/bids/99999');
        expect($response->status())->toBeIn([302, 404]); // 302 = redirect with error, 404 = not found
    });

    test('accessing bids for non-existent auction returns appropriate error', function () {
        $response = $this->get('/auctions/99999/bids');
        expect($response->status())->toBeIn([302, 404]); // 302 = redirect to login, 404 = not found
    });

    test('accessing highest bid for non-existent auction returns appropriate error', function () {
        $response = $this->get('/auctions/99999/highest-bid');
        expect($response->status())->toBeIn([404, 500]); // Should be 404, but 500 indicates a bug

        // If it's 500, that's a server error we found!
        if ($response->status() === 500) {
            $this->markTestIncomplete('Found 500 error - this needs to be fixed to return 404');
        }
    });

    test('accessing recent bids for non-existent auction returns appropriate error', function () {
        $response = $this->get('/auctions/99999/recent-bids');
        expect($response->status())->toBeIn([404, 500]); // Should be 404, but 500 indicates a bug

        if ($response->status() === 500) {
            $this->markTestIncomplete('Found 500 error - this needs to be fixed to return 404');
        }
    });

    test('accessing winning bid for non-existent auction returns appropriate error', function () {
        $response = $this->get('/auctions/99999/winning-bid');
        expect($response->status())->toBeIn([404, 500]); // Should be 404, but 500 indicates a bug

        if ($response->status() === 500) {
            $this->markTestIncomplete('Found 500 error - this needs to be fixed to return 404');
        }
    });

    test('watchlist operations on non-existent auction returns appropriate error', function () {
        $user = createTestUser();
        $this->actingAs($user);

        $response = $this->get('/watchlist/check/99999');
        expect($response->status())->toBeIn([404, 500]); // Should be 404, but 500 indicates a bug

        if ($response->status() === 500) {
            $this->markTestIncomplete('Found 500 error in watchlist check - this needs to be fixed');
        }

        $response = $this->post('/watchlist/toggle/99999');
        expect($response->status())->toBeIn([404, 500]); // Should be 404, but 500 indicates a bug

        $response = $this->delete('/watchlist/99999');
        expect($response->status())->toBeIn([302, 404, 500]); // 302 = redirect with error, 404 = not found, 500 = bug
    });

    test('uploading images to non-existent auction returns appropriate error', function () {
        $user = createTestUser();
        $this->actingAs($user);

        $response = $this->post('/auctions/99999/images', []);
        expect($response->status())->toBeIn([302, 404, 422]); // 302 = validation redirect, 404 = not found, 422 = validation error
    });

    test('activating non-existent auction returns appropriate error', function () {
        $user = createTestUser();
        $this->actingAs($user);

        $response = $this->post('/auctions/99999/activate');
        expect($response->status())->toBeIn([302, 404, 500]); // 302 = redirect with error, 404 = not found, 500 = bug

        if ($response->status() === 500) {
            $this->markTestIncomplete('Found 500 error in auction activation - this needs to be fixed');
        }
    });

    test('ending non-existent auction returns appropriate error', function () {
        $user = createTestUser();
        $this->actingAs($user);

        $response = $this->post('/auctions/99999/end');
        expect($response->status())->toBeIn([302, 404, 500]); // 302 = redirect with error, 404 = not found, 500 = bug

        if ($response->status() === 500) {
            $this->markTestIncomplete('Found 500 error in auction ending - this needs to be fixed');
        }
    });

    test('creating payment for non-existent auction returns 404', function () {
        $user = createTestUser();
        $this->actingAs($user);

        $response = $this->get('/payments/create/99999');
        expect($response->status())->toBeNotFound();
    });
});

describe('Authentication Error Tests', function () {

    test('accessing protected routes without authentication returns redirect', function () {
        $protectedRoutes = [
            '/dashboard',
            '/auctions/create',
            '/my/auctions',
            '/my/bids',
            '/my/won-auctions',
            '/my/profile',
            '/watchlist',
            '/payments',
        ];

        foreach ($protectedRoutes as $route) {
            $response = $this->get($route);
            expect($response->status())->toBeIn([302, 404]); // 302 = redirect to login, 404 = route not found
        }
    });

    test('accessing protected POST routes without authentication returns redirect', function () {
        $protectedRoutes = [
            '/auctions',
            '/bids',
            '/watchlist',
            '/payments',
        ];

        foreach ($protectedRoutes as $route) {
            $response = $this->post($route, []);
            expect($response->status())->toBe(302); // Redirect to login
        }
    });
});

describe('API Error Detection Tests', function () {

    test('API non-existent auction returns 404', function () {
        $response = $this->getJson('/api/v1/auctions/99999');
        expect($response->status())->toBeNotFound();
    });

    test('API non-existent category returns 404', function () {
        $response = $this->getJson('/api/v1/categories/99999');
        expect($response->status())->toBeNotFound();
    });

    test('API non-existent category auctions returns 404', function () {
        $response = $this->getJson('/api/v1/categories/99999/auctions');
        expect($response->status())->toBeNotFound();
    });

    test('API non-existent bid returns 404', function () {
        $user = createTestUser();
        $this->actingAs($user);

        $response = $this->getJson('/api/v1/bids/99999');
        expect($response->status())->toBeNotFound();
    });

    test('API accessing auction bids for non-existent auction returns 404', function () {
        $response = $this->getJson('/api/v1/auctions/99999/bids');
        expect($response->status())->toBeNotFound();
    });

    test('API accessing auction statistics for non-existent auction returns 404', function () {
        $response = $this->getJson('/api/v1/auctions/99999/statistics');
        expect($response->status())->toBeNotFound();
    });

    test('API highest bid for non-existent auction returns 404', function () {
        $response = $this->getJson('/api/v1/auctions/99999/highest-bid');
        expect($response->status())->toBeNotFound();
    });

    test('API winning bid for non-existent auction returns 404', function () {
        $response = $this->getJson('/api/v1/auctions/99999/winning-bid');
        expect($response->status())->toBeNotFound();
    });

    test('API recent bids for non-existent auction returns 404', function () {
        $response = $this->getJson('/api/v1/auctions/99999/recent-bids');
        expect($response->status())->toBeNotFound();
    });
});

describe('Input Validation Error Tests', function () {

    test('creating auction with invalid data returns validation errors', function () {
        // Ensure user auction creation is enabled for this test
        \App\Models\Setting::set('allow_user_auction_creation', true, 'boolean');

        $user = createTestUser();
        $this->actingAs($user);

        $response = $this->post('/auctions', []);
        expect($response->status())->toBeIn([302, 403]); // 302 = redirect with validation errors, 403 = authorization failed
    });

    test('creating bid with invalid data returns validation errors', function () {
        $user = createTestUser();
        $this->actingAs($user);

        $response = $this->post('/bids', []);
        expect($response->status())->toBe(302); // Redirect with validation errors
    });

    test('API creating auction with invalid data returns 422', function () {
        $user = createTestUser();
        $token = $user->createToken('test')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/v1/auctions', []);

        expect($response->status())->toBeIn([422, 401, 403, 404]); // 422 = validation, 401 = unauthorized, 403 = forbidden, 404 = route not found
    });

    test('API creating bid with invalid data returns 422', function () {
        $user = createTestUser();
        $token = $user->createToken('test')->plainTextToken;

        $response = $this->withHeaders([
            'Authorization' => 'Bearer ' . $token,
        ])->postJson('/api/v1/bids', []);

        expect($response->status())->toBeIn([422, 401, 403, 404]); // 422 = validation, 401 = unauthorized, 403 = forbidden, 404 = route not found
    });
});

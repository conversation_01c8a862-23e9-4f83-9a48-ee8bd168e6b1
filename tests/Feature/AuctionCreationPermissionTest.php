<?php

use App\Models\Setting;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;

uses(RefreshDatabase::class);

beforeEach(function () {
    // Seed default settings
    $this->artisan('db:seed', ['--class' => 'SettingsSeeder']);
});

test('admin can always create auctions regardless of global setting', function () {
    // Disable user auction creation
    Setting::set('allow_user_auction_creation', false, 'boolean');
    
    $admin = User::factory()->create([
        'role' => 'admin',
        'is_active' => true,
        'email_verified_at' => now(),
    ]);
    
    expect($admin->canCreateAuctions())->toBeTrue();
});

test('regular user can create auctions when setting is enabled', function () {
    // Enable user auction creation
    Setting::set('allow_user_auction_creation', true, 'boolean');
    
    $user = User::factory()->create([
        'role' => 'user',
        'is_active' => true,
        'email_verified_at' => now(),
    ]);
    
    expect($user->canCreateAuctions())->toBeTrue();
});

test('regular user cannot create auctions when setting is disabled', function () {
    // Disable user auction creation
    Setting::set('allow_user_auction_creation', false, 'boolean');
    
    $user = User::factory()->create([
        'role' => 'user',
        'is_active' => true,
        'email_verified_at' => now(),
    ]);
    
    expect($user->canCreateAuctions())->toBeFalse();
});

test('inactive user cannot create auctions even when setting is enabled', function () {
    Setting::set('allow_user_auction_creation', true, 'boolean');
    
    $user = User::factory()->create([
        'role' => 'user',
        'is_active' => false,
        'email_verified_at' => now(),
    ]);
    
    expect($user->canCreateAuctions())->toBeFalse();
});

test('unverified user cannot create auctions even when setting is enabled', function () {
    Setting::set('allow_user_auction_creation', true, 'boolean');
    
    $user = User::factory()->create([
        'role' => 'user',
        'is_active' => true,
        'email_verified_at' => null,
    ]);
    
    expect($user->canCreateAuctions())->toBeFalse();
});

test('auction creation form request respects permission setting', function () {
    // Disable user auction creation
    Setting::set('allow_user_auction_creation', false, 'boolean');
    
    $user = User::factory()->create([
        'role' => 'user',
        'is_active' => true,
        'email_verified_at' => now(),
    ]);
    
    $this->actingAs($user);
    
    $response = $this->post('/auctions', [
        'category_id' => 1,
        'title' => 'Test Auction',
        'description' => 'Test Description',
        'starting_price' => 10.00,
        'ends_at' => now()->addDays(7),
    ]);
    
    // Should be forbidden (403) due to authorization failure
    expect($response->status())->toBe(403);
});

test('admin can create auction even when user creation is disabled', function () {
    // Disable user auction creation
    Setting::set('allow_user_auction_creation', false, 'boolean');
    
    $admin = User::factory()->create([
        'role' => 'admin',
        'is_active' => true,
        'email_verified_at' => now(),
    ]);
    
    $this->actingAs($admin);
    
    $response = $this->post('/auctions', [
        'category_id' => 1,
        'title' => 'Admin Test Auction',
        'description' => 'Admin Test Description',
        'starting_price' => 10.00,
        'ends_at' => now()->addDays(7),
    ]);
    
    // Should succeed (redirect or 201) since admin can always create
    expect($response->status())->toBeIn([201, 302]);
});

test('auction create page is accessible to users when setting is enabled', function () {
    Setting::set('allow_user_auction_creation', true, 'boolean');
    
    $user = User::factory()->create([
        'role' => 'user',
        'is_active' => true,
        'email_verified_at' => now(),
    ]);
    
    $this->actingAs($user);
    
    $response = $this->get('/auctions/create');
    expect($response->status())->toBe(200);
});

test('auction create page is forbidden to users when setting is disabled', function () {
    Setting::set('allow_user_auction_creation', false, 'boolean');
    
    $user = User::factory()->create([
        'role' => 'user',
        'is_active' => true,
        'email_verified_at' => now(),
    ]);
    
    $this->actingAs($user);
    
    $response = $this->get('/auctions/create');
    expect($response->status())->toBe(403);
});

test('auction create page is always accessible to admins', function () {
    Setting::set('allow_user_auction_creation', false, 'boolean');
    
    $admin = User::factory()->create([
        'role' => 'admin',
        'is_active' => true,
        'email_verified_at' => now(),
    ]);
    
    $this->actingAs($admin);
    
    $response = $this->get('/auctions/create');
    expect($response->status())->toBe(200);
});

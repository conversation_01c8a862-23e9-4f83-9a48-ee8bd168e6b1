<?php

use App\Models\User;
use App\Models\Auction;
use App\Models\Category;
use App\Models\Bid;

describe('Public Route Coverage Tests', function () {
    
    test('home page loads successfully', function () {
        $response = $this->get('/');
        expect($response->status())->toBe(200);
    });

    test('public auction routes load successfully', function () {
        $category = createTestCategory();
        $user = createTestUser();
        $auction = createTestAuction([
            'user_id' => $user->id,
            'category_id' => $category->id,
        ]);

        $routes = [
            '/auctions',
            '/auctions/featured',
            '/auctions/ending-soon',
            "/auctions/{$auction->id}",
        ];

        foreach ($routes as $route) {
            $response = $this->get($route);
            expect($response->status())->toBeIn([200, 302]); // Success or redirect
        }
    });

    test('public category routes load successfully', function () {
        $category = createTestCategory();

        $routes = [
            '/categories',
            "/categories/{$category->slug}",
            "/categories/{$category->slug}/auctions",
        ];

        foreach ($routes as $route) {
            $response = $this->get($route);
            expect($response->status())->toBeIn([200, 302]);
        }
    });

    test('public bid information routes load successfully', function () {
        $category = createTestCategory();
        $user = createTestUser();
        $auction = createTestAuction([
            'user_id' => $user->id,
            'category_id' => $category->id,
        ]);

        $routes = [
            "/auctions/{$auction->id}/highest-bid",
            "/auctions/{$auction->id}/recent-bids",
            "/auctions/{$auction->id}/winning-bid",
        ];

        foreach ($routes as $route) {
            $response = $this->get($route);
            expect($response->status())->toBeIn([200, 404]); // Success or not found (no bids yet)
        }
    });
});

describe('Authenticated Route Coverage Tests', function () {
    
    test('dashboard and user routes load successfully for authenticated user', function () {
        $user = createTestUser();
        $this->actingAs($user);

        $routes = [
            '/dashboard',
            '/my/auctions',
            '/my/bids',
            '/my/won-auctions',
            '/my/profile',
        ];

        foreach ($routes as $route) {
            $response = $this->get($route);
            expect($response->status())->toBe(200);
        }
    });

    test('auction management routes load successfully for authenticated user', function () {
        $user = createTestUser();
        $category = createTestCategory();
        $auction = createTestAuction([
            'user_id' => $user->id,
            'category_id' => $category->id,
        ]);
        
        $this->actingAs($user);

        $routes = [
            '/auctions/create',
            "/auctions/{$auction->id}/edit",
        ];

        foreach ($routes as $route) {
            $response = $this->get($route);
            expect($response->status())->toBe(200);
        }
    });

    test('watchlist routes load successfully for authenticated user', function () {
        $user = createTestUser();
        $category = createTestCategory();
        $auctionOwner = createTestUser();
        $auction = createTestAuction([
            'user_id' => $auctionOwner->id,
            'category_id' => $category->id,
        ]);
        
        $this->actingAs($user);

        $routes = [
            '/watchlist',
            "/watchlist/check/{$auction->id}",
        ];

        foreach ($routes as $route) {
            $response = $this->get($route);
            expect($response->status())->toBeIn([200, 404]);
        }
    });

    test('payment routes load successfully for authenticated user', function () {
        $user = createTestUser();
        $category = createTestCategory();
        $auctionOwner = createTestUser();
        $auction = createTestAuction([
            'user_id' => $auctionOwner->id,
            'category_id' => $category->id,
        ]);
        
        $this->actingAs($user);

        $routes = [
            '/payments',
            "/payments/create/{$auction->id}",
        ];

        foreach ($routes as $route) {
            $response = $this->get($route);
            expect($response->status())->toBeIn([200, 403, 404]); // Success, forbidden, or not found
        }
    });

    test('bid routes load successfully for authenticated user', function () {
        $user = createTestUser();
        $category = createTestCategory();
        $auctionOwner = createTestUser();
        $auction = createTestAuction([
            'user_id' => $auctionOwner->id,
            'category_id' => $category->id,
        ]);
        
        $this->actingAs($user);

        $response = $this->get("/auctions/{$auction->id}/bids");
        expect($response->status())->toBeIn([200, 404]);
    });
});

describe('API Route Coverage Tests', function () {
    
    test('public API routes load successfully', function () {
        $category = createTestCategory();
        $user = createTestUser();
        $auction = createTestAuction([
            'user_id' => $user->id,
            'category_id' => $category->id,
        ]);

        $routes = [
            '/api/auctions',
            '/api/auctions/featured',
            '/api/auctions/ending-soon',
            "/api/auctions/{$auction->id}",
            "/api/auctions/{$auction->id}/bids",
            "/api/auctions/{$auction->id}/statistics",
            '/api/categories',
            "/api/categories/{$category->id}",
            "/api/categories/{$category->id}/auctions",
        ];

        foreach ($routes as $route) {
            $response = $this->getJson($route);
            expect($response->status())->toBeIn([200, 404]);
        }
    });

    test('authenticated API routes load successfully', function () {
        $user = createTestUser();
        $category = createTestCategory();
        $auction = createTestAuction([
            'user_id' => $user->id,
            'category_id' => $category->id,
        ]);
        
        $this->actingAs($user);

        $routes = [
            '/api/user/auctions',
            '/api/user/watched-auctions',
            '/api/user/won-auctions',
            '/api/user/bids',
            '/api/user/winning-bids',
            '/api/user/outbid-bids',
            '/api/user/bid-statistics',
            '/api/user/profile',
            '/api/user/notifications',
            '/api/user/watchlist',
            '/api/user/payments',
        ];

        foreach ($routes as $route) {
            $response = $this->getJson($route);
            expect($response->status())->toBeIn([200, 404]);
        }
    });

    test('API bid information routes load successfully', function () {
        $category = createTestCategory();
        $user = createTestUser();
        $auction = createTestAuction([
            'user_id' => $user->id,
            'category_id' => $category->id,
        ]);

        $routes = [
            "/api/auctions/{$auction->id}/highest-bid",
            "/api/auctions/{$auction->id}/winning-bid",
            "/api/auctions/{$auction->id}/recent-bids",
        ];

        foreach ($routes as $route) {
            $response = $this->getJson($route);
            expect($response->status())->toBeIn([200, 404]);
        }
    });
});

describe('Settings Route Coverage Tests', function () {
    
    test('settings routes load successfully for authenticated user', function () {
        $user = createTestUser();
        $this->actingAs($user);

        // These routes are likely defined in routes/settings.php
        $settingsRoutes = [
            '/settings',
            '/settings/profile',
            '/settings/account',
            '/settings/notifications',
            '/settings/privacy',
            '/settings/security',
        ];

        foreach ($settingsRoutes as $route) {
            $response = $this->get($route);
            expect($response->status())->toBeIn([200, 404]); // 404 if route doesn't exist yet
        }
    });
});

describe('Auth Route Coverage Tests', function () {
    
    test('authentication routes load successfully', function () {
        // These routes are likely defined in routes/auth.php
        $authRoutes = [
            '/login',
            '/register',
            '/forgot-password',
            '/reset-password',
        ];

        foreach ($authRoutes as $route) {
            $response = $this->get($route);
            expect($response->status())->toBeIn([200, 302, 404]); // Success, redirect, or not found
        }
    });

    test('email verification routes handle appropriately', function () {
        $routes = [
            '/email/verify',
            '/email/verification-notification',
        ];

        foreach ($routes as $route) {
            $response = $this->get($route);
            expect($response->status())->toBeIn([200, 302, 404]); // Success, redirect, or not found
        }
    });
});

describe('Error Page Coverage Tests', function () {
    
    test('custom error pages load successfully', function () {
        // Test if custom error pages exist and load properly
        $errorRoutes = [
            '/404',
            '/500',
            '/403',
        ];

        foreach ($errorRoutes as $route) {
            $response = $this->get($route);
            // These might not exist as explicit routes, so we expect 404
            expect($response->status())->toBeIn([200, 404]);
        }
    });
});

describe('Webhook Route Coverage Tests', function () {
    
    test('webhook routes handle requests appropriately', function () {
        // Test webhook endpoint
        $response = $this->post('/payments/webhook', []);
        expect($response->status())->toBeIn([200, 400, 422]); // Success, bad request, or validation error
    });
});

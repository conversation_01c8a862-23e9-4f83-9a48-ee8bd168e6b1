<?php

use App\Models\User;
use App\Models\Auction;
use App\Models\Category;
use App\Models\Bid;
use Illuminate\Support\Facades\DB;

describe('Database Constraint Error Tests', function () {
    
    test('creating auction with non-existent category returns error', function () {
        $user = createTestUser();
        $this->actingAs($user);
        
        $response = $this->post('/auctions', [
            'title' => 'Test Auction',
            'description' => 'Test Description',
            'category_id' => 99999, // Non-existent category
            'starting_price' => 10.00,
            'end_time' => now()->addDays(7)->format('Y-m-d H:i:s'),
        ]);
        
        expect($response->status())->toBeIn([400, 422, 500]);
    });

    test('creating bid with non-existent auction returns error', function () {
        $user = createTestUser();
        $this->actingAs($user);
        
        $response = $this->post('/bids', [
            'auction_id' => 99999, // Non-existent auction
            'amount' => 100.00,
        ]);
        
        expect($response->status())->toBeIn([400, 422, 500]);
    });

    test('creating bid with non-existent user returns error', function () {
        $auctionOwner = createTestUser();
        $category = createTestCategory();
        
        $auction = createTestAuction([
            'user_id' => $auctionOwner->id,
            'category_id' => $category->id,
        ]);
        
        // Simulate a request with invalid user context
        $response = $this->post('/bids', [
            'auction_id' => $auction->id,
            'amount' => 100.00,
        ]);
        
        expect($response->status())->toBeIn([302, 401, 403]); // Redirect to login or unauthorized
    });
});

describe('File Upload Error Tests', function () {
    
    test('uploading invalid file type to auction images returns error', function () {
        $user = createTestUser();
        $category = createTestCategory();
        
        $auction = createTestAuction([
            'user_id' => $user->id,
            'category_id' => $category->id,
        ]);
        
        $this->actingAs($user);
        
        // Create a fake text file instead of image
        $file = \Illuminate\Http\UploadedFile::fake()->create('document.txt', 100, 'text/plain');
        
        $response = $this->post("/auctions/{$auction->id}/images", [
            'images' => [$file],
        ]);
        
        expect($response->status())->toBeIn([400, 422]);
    });

    test('uploading oversized file to auction images returns error', function () {
        $user = createTestUser();
        $category = createTestCategory();
        
        $auction = createTestAuction([
            'user_id' => $user->id,
            'category_id' => $category->id,
        ]);
        
        $this->actingAs($user);
        
        // Create a fake oversized image (10MB)
        $file = \Illuminate\Http\UploadedFile::fake()->image('large-image.jpg')->size(10240);
        
        $response = $this->post("/auctions/{$auction->id}/images", [
            'images' => [$file],
        ]);
        
        expect($response->status())->toBeIn([400, 422]);
    });
});

describe('Concurrent Access Error Tests', function () {
    
    test('multiple users bidding simultaneously on same auction handles gracefully', function () {
        $auctionOwner = createTestUser();
        $bidder1 = createTestUser();
        $bidder2 = createTestUser();
        $category = createTestCategory();
        
        $auction = createTestAuction([
            'user_id' => $auctionOwner->id,
            'category_id' => $category->id,
            'status' => 'active',
            'current_bid' => 50.00,
        ]);
        
        // Simulate concurrent bids
        $this->actingAs($bidder1);
        $response1 = $this->post('/bids', [
            'auction_id' => $auction->id,
            'amount' => 100.00,
        ]);
        
        $this->actingAs($bidder2);
        $response2 = $this->post('/bids', [
            'auction_id' => $auction->id,
            'amount' => 100.00, // Same amount
        ]);
        
        // At least one should succeed, one might fail due to race condition
        expect([$response1->status(), $response2->status()])->toContain(200);
    });
});

describe('Memory and Performance Error Tests', function () {
    
    test('accessing auction with many bids does not cause memory issues', function () {
        $auctionOwner = createTestUser();
        $category = createTestCategory();
        
        $auction = createTestAuction([
            'user_id' => $auctionOwner->id,
            'category_id' => $category->id,
        ]);
        
        // Create many bids (but not too many for test performance)
        for ($i = 0; $i < 50; $i++) {
            $bidder = createTestUser();
            createTestBid([
                'user_id' => $bidder->id,
                'auction_id' => $auction->id,
                'amount' => 10.00 + $i,
            ]);
        }
        
        $response = $this->get("/auctions/{$auction->id}");
        expect($response->status())->toBe(200);
        
        $response = $this->get("/auctions/{$auction->id}/bids");
        expect($response->status())->toBe(200);
    });

    test('accessing category with many auctions does not cause memory issues', function () {
        $category = createTestCategory();
        
        // Create many auctions
        for ($i = 0; $i < 50; $i++) {
            $user = createTestUser();
            createTestAuction([
                'user_id' => $user->id,
                'category_id' => $category->id,
            ]);
        }
        
        $response = $this->get("/categories/{$category->slug}");
        expect($response->status())->toBe(200);
        
        $response = $this->get("/categories/{$category->slug}/auctions");
        expect($response->status())->toBe(200);
    });
});

describe('API Rate Limiting and Abuse Tests', function () {
    
    test('rapid successive requests to same endpoint handle gracefully', function () {
        $responses = [];
        
        // Make 10 rapid requests
        for ($i = 0; $i < 10; $i++) {
            $responses[] = $this->get('/auctions');
        }
        
        // All should either succeed or be rate limited (429)
        foreach ($responses as $response) {
            expect($response->status())->toBeIn([200, 429]);
        }
    });

    test('API endpoints handle rapid successive requests gracefully', function () {
        $responses = [];
        
        // Make 10 rapid API requests
        for ($i = 0; $i < 10; $i++) {
            $responses[] = $this->getJson('/api/auctions');
        }
        
        // All should either succeed or be rate limited
        foreach ($responses as $response) {
            expect($response->status())->toBeIn([200, 429]);
        }
    });
});

describe('Malformed Request Error Tests', function () {
    
    test('sending malformed JSON to API endpoints returns appropriate error', function () {
        $user = createTestUser();
        $this->actingAs($user);
        
        // Send malformed JSON
        $response = $this->call('POST', '/api/auctions', [], [], [], [
            'CONTENT_TYPE' => 'application/json',
        ], '{"invalid": json}');
        
        expect($response->status())->toBeIn([400, 422]);
    });

    test('sending extremely large request body returns appropriate error', function () {
        $user = createTestUser();
        $this->actingAs($user);
        
        // Create a very large string
        $largeData = str_repeat('a', 1024 * 1024); // 1MB of 'a'
        
        $response = $this->postJson('/api/auctions', [
            'title' => $largeData,
            'description' => $largeData,
        ]);
        
        expect($response->status())->toBeIn([400, 413, 422]); // Bad request, payload too large, or validation error
    });

    test('sending request with invalid content type returns appropriate error', function () {
        $user = createTestUser();
        $this->actingAs($user);
        
        $response = $this->call('POST', '/api/auctions', [], [], [], [
            'CONTENT_TYPE' => 'application/xml',
        ], '<xml>invalid</xml>');
        
        expect($response->status())->toBeIn([400, 415, 422]); // Bad request, unsupported media type, or validation error
    });
});

describe('Session and State Error Tests', function () {
    
    test('accessing protected routes with expired session handles gracefully', function () {
        $user = createTestUser();
        $this->actingAs($user);
        
        // Simulate session expiry by clearing session
        session()->flush();
        
        $response = $this->get('/dashboard');
        expect($response->status())->toBeIn([302, 401]); // Redirect to login or unauthorized
    });

    test('CSRF token mismatch returns appropriate error', function () {
        $user = createTestUser();
        $this->actingAs($user);
        
        // Make request without CSRF token
        $response = $this->call('POST', '/auctions', [
            'title' => 'Test Auction',
            'description' => 'Test Description',
        ], [], [], [
            'HTTP_X_CSRF_TOKEN' => 'invalid-token',
        ]);
        
        expect($response->status())->toBeIn([419, 422]); // CSRF token mismatch or validation error
    });
});

<?php

declare(strict_types=1);

namespace Tests\Integration\Infrastructure\Repositories;

use App\Domain\Auction\Models\Auction as DomainAuction;
use App\Domain\Auction\ValueObjects\AuctionDuration;
use App\Domain\Auction\ValueObjects\ReservePrice;
use App\Domain\Shared\ValueObjects\Id;
use App\Domain\Shared\ValueObjects\Money;
use App\Domain\Shared\ValueObjects\Timestamp;
use App\Domain\User\ValueObjects\UserId;
use App\Infrastructure\Repositories\EloquentAuctionRepository;
use App\Models\Auction;
use App\Models\Category;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class EloquentAuctionRepositoryTest extends TestCase
{
    use RefreshDatabase;

    private EloquentAuctionRepository $repository;
    private User $testUser;
    private Category $testCategory;

    protected function setUp(): void
    {
        parent::setUp();
        
        $this->repository = new EloquentAuctionRepository();
        
        // Create test user
        $this->testUser = User::factory()->create([
            'role' => 'seller',
            'email_verified_at' => now(),
            'is_active' => true,
        ]);
        
        // Create test category
        $this->testCategory = Category::factory()->create([
            'name' => 'Test Category',
            'is_active' => true,
        ]);
    }

    private function createTestDomainAuction(): DomainAuction
    {
        $id = Id::generate();
        $sellerId = UserId::fromString((string) $this->testUser->id);
        $categoryId = Id::fromString((string) $this->testCategory->id);
        $startingPrice = new Money(10000, 'USD'); // $100.00
        $startTime = Timestamp::now();
        $endTime = Timestamp::fromString($startTime->value()->addDays(7)->toISOString());
        $duration = new AuctionDuration($startTime, $endTime);
        $reservePrice = ReservePrice::fromFloat(150.00, 'USD');

        return new DomainAuction(
            $id,
            $sellerId,
            $categoryId,
            'Test Auction',
            'A comprehensive test auction description that meets the minimum length requirements',
            $startingPrice,
            $duration,
            $reservePrice
        );
    }

    public function test_can_save_domain_auction(): void
    {
        $domainAuction = $this->createTestDomainAuction();

        $this->repository->save($domainAuction);

        $this->assertDatabaseHas('auctions', [
            'id' => $domainAuction->id()->value(),
            'title' => 'Test Auction',
            'user_id' => $this->testUser->id,
            'category_id' => $this->testCategory->id,
            'starting_price' => 10000,
            'reserve_price' => 15000,
        ]);
    }

    public function test_can_find_auction_by_id(): void
    {
        $domainAuction = $this->createTestDomainAuction();
        $this->repository->save($domainAuction);

        $foundAuction = $this->repository->findById($domainAuction->id());

        $this->assertNotNull($foundAuction);
        $this->assertEquals($domainAuction->id()->value(), $foundAuction->id()->value());
        $this->assertEquals($domainAuction->title(), $foundAuction->title());
    }

    public function test_returns_null_when_auction_not_found(): void
    {
        $nonExistentId = Id::generate();

        $result = $this->repository->findById($nonExistentId);

        $this->assertNull($result);
    }

    public function test_can_find_auctions_by_seller(): void
    {
        $domainAuction1 = $this->createTestDomainAuction();
        $domainAuction2 = $this->createTestDomainAuction();
        
        $this->repository->save($domainAuction1);
        $this->repository->save($domainAuction2);

        $sellerId = UserId::fromString((string) $this->testUser->id);
        $auctions = $this->repository->findBySeller($sellerId);

        $this->assertCount(2, $auctions);
    }

    public function test_can_find_auctions_by_category(): void
    {
        $domainAuction = $this->createTestDomainAuction();
        $this->repository->save($domainAuction);

        $categoryId = Id::fromString((string) $this->testCategory->id);
        $auctions = $this->repository->findByCategory($categoryId);

        $this->assertCount(1, $auctions);
        $this->assertEquals($domainAuction->id()->value(), $auctions->first()->id()->value());
    }

    public function test_can_find_active_auctions(): void
    {
        // Create and save an active auction
        $domainAuction = $this->createTestDomainAuction();
        $this->repository->save($domainAuction);
        
        // Update the auction to be active in the database
        Auction::where('id', $domainAuction->id()->value())->update(['status' => 'active']);

        $activeAuctions = $this->repository->findActive();

        $this->assertCount(1, $activeAuctions);
    }

    public function test_can_find_ending_soon_auctions(): void
    {
        // Create auction ending in 2 hours
        $id = Id::generate();
        $sellerId = UserId::fromString((string) $this->testUser->id);
        $categoryId = Id::fromString((string) $this->testCategory->id);
        $startingPrice = new Money(10000, 'USD');
        $startTime = Timestamp::now();
        $endTime = Timestamp::fromString($startTime->value()->addHours(2)->toISOString());
        $duration = new AuctionDuration($startTime, $endTime);
        $reservePrice = ReservePrice::none();

        $domainAuction = new DomainAuction(
            $id,
            $sellerId,
            $categoryId,
            'Ending Soon Auction',
            'An auction that will end soon for testing purposes',
            $startingPrice,
            $duration,
            $reservePrice
        );

        $this->repository->save($domainAuction);
        
        // Update to active status
        Auction::where('id', $domainAuction->id()->value())->update(['status' => 'active']);

        $endingSoonAuctions = $this->repository->findEndingSoon(6); // Within 6 hours

        $this->assertCount(1, $endingSoonAuctions);
    }

    public function test_can_search_auctions(): void
    {
        $domainAuction = $this->createTestDomainAuction();
        $this->repository->save($domainAuction);

        $results = $this->repository->search('Test Auction', [], 10);

        $this->assertGreaterThan(0, $results->count());
    }

    public function test_can_update_existing_auction(): void
    {
        $domainAuction = $this->createTestDomainAuction();
        $this->repository->save($domainAuction);

        // Modify the auction
        $reflection = new \ReflectionClass($domainAuction);
        $titleProperty = $reflection->getProperty('title');
        $titleProperty->setAccessible(true);
        $titleProperty->setValue($domainAuction, 'Updated Test Auction');

        $this->repository->save($domainAuction);

        $this->assertDatabaseHas('auctions', [
            'id' => $domainAuction->id()->value(),
            'title' => 'Updated Test Auction',
        ]);
    }

    public function test_can_delete_auction(): void
    {
        $domainAuction = $this->createTestDomainAuction();
        $this->repository->save($domainAuction);

        $this->assertDatabaseHas('auctions', [
            'id' => $domainAuction->id()->value(),
        ]);

        $this->repository->delete($domainAuction);

        $this->assertDatabaseMissing('auctions', [
            'id' => $domainAuction->id()->value(),
        ]);
    }

    public function test_can_count_auctions_by_status(): void
    {
        $domainAuction1 = $this->createTestDomainAuction();
        $domainAuction2 = $this->createTestDomainAuction();
        
        $this->repository->save($domainAuction1);
        $this->repository->save($domainAuction2);

        // Both should be scheduled by default
        $count = $this->repository->countByStatus('scheduled');

        $this->assertEquals(2, $count);
    }

    public function test_can_find_featured_auctions(): void
    {
        $domainAuction = $this->createTestDomainAuction();
        $featuredUntil = Timestamp::fromString(now()->addDays(7)->toISOString());
        $domainAuction->feature($featuredUntil);
        
        $this->repository->save($domainAuction);

        $featuredAuctions = $this->repository->findFeatured();

        $this->assertCount(1, $featuredAuctions);
        $this->assertTrue($featuredAuctions->first()->featured());
    }

    public function test_repository_handles_domain_model_conversion(): void
    {
        $domainAuction = $this->createTestDomainAuction();
        $this->repository->save($domainAuction);

        $foundAuction = $this->repository->findById($domainAuction->id());

        // Verify that the found auction is a proper domain model
        $this->assertInstanceOf(DomainAuction::class, $foundAuction);
        $this->assertEquals($domainAuction->title(), $foundAuction->title());
        $this->assertEquals($domainAuction->description(), $foundAuction->description());
        $this->assertEquals($domainAuction->startingPrice()->amount(), $foundAuction->startingPrice()->amount());
        $this->assertEquals($domainAuction->reservePrice()->amount(), $foundAuction->reservePrice()->amount());
    }

    public function test_can_find_expired_active_auctions(): void
    {
        // Create auction that ended 1 hour ago
        $id = Id::generate();
        $sellerId = UserId::fromString((string) $this->testUser->id);
        $categoryId = Id::fromString((string) $this->testCategory->id);
        $startingPrice = new Money(10000, 'USD');
        $startTime = Timestamp::fromString(now()->subDays(8)->toISOString());
        $endTime = Timestamp::fromString(now()->subHour()->toISOString());
        $duration = new AuctionDuration($startTime, $endTime);
        $reservePrice = ReservePrice::none();

        $domainAuction = new DomainAuction(
            $id,
            $sellerId,
            $categoryId,
            'Expired Auction',
            'An auction that should have ended but is still active',
            $startingPrice,
            $duration,
            $reservePrice
        );

        $this->repository->save($domainAuction);
        
        // Set as active even though it's past end time
        Auction::where('id', $domainAuction->id()->value())->update(['status' => 'active']);

        $expiredAuctions = $this->repository->findExpiredActive();

        $this->assertCount(1, $expiredAuctions);
    }
}

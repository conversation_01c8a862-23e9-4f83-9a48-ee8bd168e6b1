<?php

namespace Database\Factories;

use App\Models\Auction;
use App\Models\Bid;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Bid>
 */
class BidFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'user_id' => User::factory(),
            'auction_id' => Auction::factory(),
            'amount' => fake()->randomFloat(2, 10, 1000),
            'max_bid' => fake()->optional()->randomFloat(2, 50, 2000),
            'bid_type' => fake()->randomElement(['manual', 'proxy', 'auto']),
            'timestamp' => fake()->dateTimeBetween('-1 week', 'now'),
            'is_winning' => false,
            'is_valid' => true,
            'invalidated_at' => null,
            'invalidation_reason' => null,
            'ip_address' => fake()->ipv4(),
            'user_agent' => fake()->userAgent(),
        ];
    }

    /**
     * Indicate that the bid should be winning.
     */
    public function winning(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_winning' => true,
            'is_outbid' => false,
        ]);
    }

    /**
     * Indicate that the bid should be invalidated.
     */
    public function invalidated(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_winning' => false,
            'is_valid' => false,
            'invalidated_at' => fake()->dateTimeBetween('-1 week', 'now'),
            'invalidation_reason' => fake()->sentence(),
        ]);
    }

    /**
     * Indicate that the bid should be a proxy bid.
     */
    public function proxyBid(): static
    {
        return $this->state(fn (array $attributes) => [
            'bid_type' => 'proxy',
            'max_bid' => fake()->randomFloat(2, $attributes['amount'] ?? 100, 2000),
        ]);
    }

    /**
     * Set specific user and auction for the bid.
     */
    public function forUserAndAuction(User $user, Auction $auction): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => $user->id,
            'auction_id' => $auction->id,
        ]);
    }

    /**
     * Set a specific amount for the bid.
     */
    public function withAmount(float $amount): static
    {
        return $this->state(fn (array $attributes) => [
            'amount' => $amount,
        ]);
    }
}

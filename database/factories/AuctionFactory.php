<?php

namespace Database\Factories;

use App\Models\Auction;
use App\Models\Category;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Auction>
 */
class AuctionFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $startingPrice = fake()->randomFloat(2, 10, 1000);
        $currentBid = fake()->boolean(60) ? fake()->randomFloat(2, $startingPrice, $startingPrice * 3) : $startingPrice;
        
        return [
            'user_id' => User::factory(),
            'category_id' => Category::factory(),
            'title' => fake()->sentence(4),
            'description' => fake()->paragraphs(3, true),
            'condition' => fake()->randomElement(['new', 'like_new', 'good', 'fair', 'poor']),
            'starting_price' => $startingPrice,
            'reserve_price' => fake()->boolean(30) ? fake()->randomFloat(2, $startingPrice * 1.2, $startingPrice * 2) : null,
            'current_bid' => $currentBid,
            'bid_increment' => fake()->randomFloat(2, 1, 50),
            'buyout_price' => fake()->boolean(40) ? fake()->randomFloat(2, $currentBid * 1.5, $currentBid * 3) : null,
            'start_time' => fake()->dateTimeBetween('-1 week', 'now'),
            'end_time' => fake()->dateTimeBetween('now', '+2 weeks'),
            'actual_end_time' => null,
            'status' => fake()->randomElement(['draft', 'scheduled', 'active', 'ended']),
            'auto_extend' => fake()->boolean(70),
            'extend_minutes' => fake()->randomElement([5, 10, 15, 30]),
            'shipping_cost' => fake()->randomFloat(2, 0, 50),
            'shipping_options' => ['standard', 'express'],
            'return_policy' => fake()->sentence(),
            'terms_conditions' => fake()->paragraph(),
            'featured' => fake()->boolean(10),
            'featured_until' => fake()->boolean(10) ? fake()->dateTimeBetween('now', '+1 month') : null,
            'views_count' => fake()->numberBetween(0, 1000),
            'watchers_count' => fake()->numberBetween(0, 50),
            'bids_count' => fake()->numberBetween(0, 20),
            'winner_id' => null,
            'final_price' => null,
            'commission_rate' => fake()->randomFloat(4, 0.05, 0.15),
            'commission_amount' => null,
            'payment_status' => 'pending',
            'payment_due_date' => null,
            'shipping_status' => 'pending',
            'notes' => fake()->optional()->sentence(),
            'admin_notes' => null,
        ];
    }

    /**
     * Indicate that the auction should be active.
     */
    public function active(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'active',
            'start_time' => fake()->dateTimeBetween('-1 week', 'now'),
            'end_time' => fake()->dateTimeBetween('now', '+2 weeks'),
        ]);
    }

    /**
     * Indicate that the auction should be ended.
     */
    public function ended(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'ended',
            'start_time' => fake()->dateTimeBetween('-2 weeks', '-1 week'),
            'end_time' => fake()->dateTimeBetween('-1 week', 'now'),
            'actual_end_time' => fake()->dateTimeBetween('-1 week', 'now'),
        ]);
    }

    /**
     * Indicate that the auction should be scheduled.
     */
    public function scheduled(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'scheduled',
            'start_time' => fake()->dateTimeBetween('now', '+1 week'),
            'end_time' => fake()->dateTimeBetween('+1 week', '+3 weeks'),
        ]);
    }

    /**
     * Indicate that the auction should be a draft.
     */
    public function draft(): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'draft',
        ]);
    }

    /**
     * Indicate that the auction should be featured.
     */
    public function featured(): static
    {
        return $this->state(fn (array $attributes) => [
            'featured' => true,
            'featured_until' => fake()->dateTimeBetween('now', '+1 month'),
        ]);
    }

    /**
     * Indicate that the auction should have a winner.
     */
    public function withWinner(User $winner = null): static
    {
        return $this->state(fn (array $attributes) => [
            'status' => 'ended',
            'winner_id' => $winner?->id ?? User::factory()->create()->id,
            'final_price' => fake()->randomFloat(2, 100, 5000),
            'actual_end_time' => fake()->dateTimeBetween('-1 week', 'now'),
        ]);
    }

    /**
     * Set specific user and category for the auction.
     */
    public function forUserAndCategory(User $user, Category $category): static
    {
        return $this->state(fn (array $attributes) => [
            'user_id' => $user->id,
            'category_id' => $category->id,
        ]);
    }
}

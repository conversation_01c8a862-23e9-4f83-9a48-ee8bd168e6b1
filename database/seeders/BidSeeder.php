<?php

namespace Database\Seeders;

use App\Models\Auction;
use App\Models\Bid;
use App\Models\User;
use Illuminate\Database\Seeder;

class BidSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $activeAuctions = Auction::where('status', 'active')->get();
        $bidders = User::where('role', 'user')->get();
        
        if ($activeAuctions->isEmpty() || $bidders->isEmpty()) {
            $this->command->warn('No active auctions or bidders found. Please run AuctionSeeder and UserSeeder first.');
            return;
        }

        $totalBids = 0;

        foreach ($activeAuctions as $auction) {
            $numBids = rand(1, 15); // Random number of bids per auction
            $currentPrice = $auction->starting_price;
            $bidIncrement = $auction->bid_increment;
            
            // Get random bidders for this auction
            $auctionBidders = $bidders->random(min($numBids, $bidders->count()));
            
            for ($i = 0; $i < $numBids; $i++) {
                $bidder = $auctionBidders[$i % $auctionBidders->count()];
                
                // Make sure bidder is not the seller
                if ($bidder->id === $auction->user_id) {
                    continue;
                }
                
                // Calculate bid amount
                $bidAmount = $currentPrice + $bidIncrement + rand(0, (int)($bidIncrement * 2));
                
                // Random bid type (mostly regular bids, some proxy bids)
                $bidType = rand(1, 10) > 8 ? 'proxy' : 'regular';
                $maxBid = $bidType === 'proxy' ? $bidAmount + rand(50, 200) : null;
                
                // Create bid timestamp within auction duration
                $auctionStart = $auction->start_time;
                $auctionEnd = $auction->end_time;
                $bidTime = $auctionStart->copy()->addSeconds(
                    rand(0, $auctionEnd->diffInSeconds($auctionStart))
                );
                
                $bid = Bid::create([
                    'auction_id' => $auction->id,
                    'user_id' => $bidder->id,
                    'amount' => $bidAmount,
                    'max_bid' => $maxBid,
                    'bid_type' => $bidType,
                    'timestamp' => $bidTime,
                    'is_winning' => false, // Will be updated later
                    'is_valid' => true,
                    'ip_address' => $this->generateRandomIP(),
                    'user_agent' => $this->generateRandomUserAgent(),
                    'created_at' => $bidTime,
                    'updated_at' => $bidTime,
                ]);
                
                $currentPrice = $bidAmount;
                $totalBids++;
            }
            
            // Update auction with final bid information
            $lastBid = Bid::where('auction_id', $auction->id)
                ->orderBy('amount', 'desc')
                ->first();
                
            if ($lastBid) {
                // Mark the highest bid as winning
                $lastBid->update(['is_winning' => true]);
                
                // Update auction current bid and bid count
                $auction->update([
                    'current_bid' => $lastBid->amount,
                    'bids_count' => Bid::where('auction_id', $auction->id)->count(),
                ]);
            }
        }

        // Create some bids for ended auctions too
        $endedAuctions = Auction::where('status', 'ended')->limit(5)->get();
        
        foreach ($endedAuctions as $auction) {
            $numBids = rand(5, 20);
            $currentPrice = $auction->starting_price;
            $bidIncrement = $auction->bid_increment;
            
            $auctionBidders = $bidders->random(min($numBids, $bidders->count()));
            
            for ($i = 0; $i < $numBids; $i++) {
                $bidder = $auctionBidders[$i % $auctionBidders->count()];
                
                if ($bidder->id === $auction->user_id) {
                    continue;
                }
                
                $bidAmount = $currentPrice + $bidIncrement + rand(0, (int)($bidIncrement * 2));
                $bidType = rand(1, 10) > 8 ? 'proxy' : 'regular';
                $maxBid = $bidType === 'proxy' ? $bidAmount + rand(50, 200) : null;
                
                // Bid time within auction duration
                $auctionStart = $auction->start_time;
                $auctionEnd = $auction->end_time;
                $bidTime = $auctionStart->copy()->addSeconds(
                    rand(0, $auctionEnd->diffInSeconds($auctionStart))
                );
                
                Bid::create([
                    'auction_id' => $auction->id,
                    'user_id' => $bidder->id,
                    'amount' => $bidAmount,
                    'max_bid' => $maxBid,
                    'bid_type' => $bidType,
                    'timestamp' => $bidTime,
                    'is_winning' => $i === $numBids - 1, // Last bid wins
                    'is_valid' => true,
                    'ip_address' => $this->generateRandomIP(),
                    'user_agent' => $this->generateRandomUserAgent(),
                    'created_at' => $bidTime,
                    'updated_at' => $bidTime,
                ]);
                
                $currentPrice = $bidAmount;
                $totalBids++;
            }
            
            // Update ended auction with final information
            $winningBid = Bid::where('auction_id', $auction->id)
                ->where('is_winning', true)
                ->first();
                
            if ($winningBid) {
                $auction->update([
                    'current_bid' => $winningBid->amount,
                    'final_price' => $winningBid->amount,
                    'winner_id' => $winningBid->user_id,
                    'bids_count' => Bid::where('auction_id', $auction->id)->count(),
                    'actual_end_time' => $auction->end_time,
                ]);
            }
        }

        $this->command->info("Created {$totalBids} bids");
    }

    /**
     * Generate a random IP address
     */
    private function generateRandomIP(): string
    {
        return rand(1, 255) . '.' . rand(1, 255) . '.' . rand(1, 255) . '.' . rand(1, 255);
    }

    /**
     * Generate a random user agent
     */
    private function generateRandomUserAgent(): string
    {
        $userAgents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:89.0) Gecko/20100101 Firefox/89.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.1 Safari/605.1.15',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
        ];
        
        return $userAgents[array_rand($userAgents)];
    }
}

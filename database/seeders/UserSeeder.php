<?php

namespace Database\Seeders;

use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create admin user
        User::create([
            'name' => 'Admin User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'admin',
            'email_verified_at' => now(),
            'is_active' => true,
            'phone' => '+1234567890',
        ]);

        // Create moderator user
        User::create([
            'name' => 'Moderator User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'moderator',
            'email_verified_at' => now(),
            'is_active' => true,
            'phone' => '+1234567891',
        ]);

        // Create premium seller
        User::create([
            'name' => 'Premium Seller',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'premium_seller',
            'email_verified_at' => now(),
            'is_active' => true,
            'phone' => '+1234567892',
        ]);

        // Create regular sellers
        $sellers = [
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'role' => 'seller',
                'phone' => '+1555001001',
            ],
            [
                'name' => 'Sarah Johnson',
                'email' => '<EMAIL>',
                'role' => 'seller',
                'phone' => '+1555001002',
            ],
            [
                'name' => 'Michael Brown',
                'email' => '<EMAIL>',
                'role' => 'seller',
                'phone' => '+1555001003',
            ],
            [
                'name' => 'Emily Davis',
                'email' => '<EMAIL>',
                'role' => 'seller',
                'phone' => '+1555001004',
            ],
            [
                'name' => 'David Wilson',
                'email' => '<EMAIL>',
                'role' => 'seller',
                'phone' => '+1555001005',
            ],
        ];

        foreach ($sellers as $seller) {
            User::create([
                'name' => $seller['name'],
                'email' => $seller['email'],
                'password' => Hash::make('password'),
                'role' => $seller['role'],
                'email_verified_at' => now(),
                'is_active' => true,
                'phone' => $seller['phone'],
                'last_login_at' => now()->subDays(rand(1, 30)),
            ]);
        }

        // Create regular users (bidders)
        $users = [
            [
                'name' => 'Alice Cooper',
                'email' => '<EMAIL>',
                'phone' => '+1555002001',
            ],
            [
                'name' => 'Bob Martin',
                'email' => '<EMAIL>',
                'phone' => '+1555002002',
            ],
            [
                'name' => 'Carol White',
                'email' => '<EMAIL>',
                'phone' => '+1555002003',
            ],
            [
                'name' => 'Daniel Lee',
                'email' => '<EMAIL>',
                'phone' => '+1555002004',
            ],
            [
                'name' => 'Eva Green',
                'email' => '<EMAIL>',
                'phone' => '+1555002005',
            ],
            [
                'name' => 'Frank Miller',
                'email' => '<EMAIL>',
                'phone' => '+1555002006',
            ],
            [
                'name' => 'Grace Taylor',
                'email' => '<EMAIL>',
                'phone' => '+1555002007',
            ],
            [
                'name' => 'Henry Clark',
                'email' => '<EMAIL>',
                'phone' => '+1555002008',
            ],
            [
                'name' => 'Iris Anderson',
                'email' => '<EMAIL>',
                'phone' => '+1555002009',
            ],
            [
                'name' => 'Jack Thompson',
                'email' => '<EMAIL>',
                'phone' => '+1555002010',
            ],
            [
                'name' => 'Kate Rodriguez',
                'email' => '<EMAIL>',
                'phone' => '+1555002011',
            ],
            [
                'name' => 'Liam Garcia',
                'email' => '<EMAIL>',
                'phone' => '+1555002012',
            ],
            [
                'name' => 'Mia Martinez',
                'email' => '<EMAIL>',
                'phone' => '+1555002013',
            ],
            [
                'name' => 'Noah Hernandez',
                'email' => '<EMAIL>',
                'phone' => '+1555002014',
            ],
            [
                'name' => 'Olivia Lopez',
                'email' => '<EMAIL>',
                'phone' => '+1555002015',
            ],
        ];

        foreach ($users as $user) {
            User::create([
                'name' => $user['name'],
                'email' => $user['email'],
                'password' => Hash::make('password'),
                'role' => 'user',
                'email_verified_at' => rand(0, 1) ? now() : null,
                'is_active' => rand(0, 10) > 1, // 90% active
                'phone' => $user['phone'],
                'last_login_at' => rand(0, 1) ? now()->subDays(rand(1, 60)) : null,
            ]);
        }

        // Create some inactive/unverified users for testing
        for ($i = 1; $i <= 5; $i++) {
            User::create([
                'name' => "Inactive User {$i}",
                'email' => "inactive{$i}@example.com",
                'password' => Hash::make('password'),
                'role' => 'user',
                'email_verified_at' => null,
                'is_active' => false,
                'phone' => "+155500300{$i}",
            ]);
        }

        // Create test user for development
        User::create([
            'name' => 'Test User',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'role' => 'user',
            'email_verified_at' => now(),
            'is_active' => true,
            'phone' => '+1555000000',
            'last_login_at' => now(),
        ]);
    }
}

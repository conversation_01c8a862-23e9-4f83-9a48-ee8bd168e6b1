<?php

namespace Database\Seeders;

use App\Models\Category;
use Illuminate\Database\Seeder;

class CategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $categories = [
            [
                'name' => 'Electronics',
                'slug' => 'electronics',
                'description' => 'Electronic devices, gadgets, and accessories',
                'icon' => 'laptop',
                'is_active' => true,
                'is_featured' => true,
                'sort_order' => 1,
                'children' => [
                    [
                        'name' => 'Smartphones',
                        'slug' => 'smartphones',
                        'description' => 'Mobile phones and accessories',
                        'icon' => 'smartphone',
                    ],
                    [
                        'name' => 'Laptops & Computers',
                        'slug' => 'laptops-computers',
                        'description' => 'Laptops, desktops, and computer accessories',
                        'icon' => 'laptop',
                    ],
                    [
                        'name' => 'Gaming',
                        'slug' => 'gaming',
                        'description' => 'Gaming consoles, games, and accessories',
                        'icon' => 'gamepad',
                    ],
                    [
                        'name' => 'Audio & Video',
                        'slug' => 'audio-video',
                        'description' => 'Headphones, speakers, cameras, and more',
                        'icon' => 'headphones',
                    ],
                ]
            ],
            [
                'name' => 'Fashion & Accessories',
                'slug' => 'fashion-accessories',
                'description' => 'Clothing, shoes, jewelry, and fashion accessories',
                'icon' => 'shirt',
                'is_active' => true,
                'is_featured' => true,
                'sort_order' => 2,
                'children' => [
                    [
                        'name' => 'Men\'s Clothing',
                        'slug' => 'mens-clothing',
                        'description' => 'Men\'s shirts, pants, suits, and more',
                        'icon' => 'shirt',
                    ],
                    [
                        'name' => 'Women\'s Clothing',
                        'slug' => 'womens-clothing',
                        'description' => 'Women\'s dresses, tops, pants, and more',
                        'icon' => 'dress',
                    ],
                    [
                        'name' => 'Shoes',
                        'slug' => 'shoes',
                        'description' => 'Sneakers, boots, heels, and casual shoes',
                        'icon' => 'shoe',
                    ],
                    [
                        'name' => 'Jewelry & Watches',
                        'slug' => 'jewelry-watches',
                        'description' => 'Rings, necklaces, watches, and accessories',
                        'icon' => 'watch',
                    ],
                ]
            ],
            [
                'name' => 'Home & Garden',
                'slug' => 'home-garden',
                'description' => 'Furniture, decor, appliances, and garden items',
                'icon' => 'home',
                'is_active' => true,
                'sort_order' => 3,
                'children' => [
                    [
                        'name' => 'Furniture',
                        'slug' => 'furniture',
                        'description' => 'Chairs, tables, sofas, and bedroom furniture',
                        'icon' => 'chair',
                    ],
                    [
                        'name' => 'Home Decor',
                        'slug' => 'home-decor',
                        'description' => 'Art, lighting, rugs, and decorative items',
                        'icon' => 'lamp',
                    ],
                    [
                        'name' => 'Kitchen & Dining',
                        'slug' => 'kitchen-dining',
                        'description' => 'Cookware, appliances, and dining sets',
                        'icon' => 'utensils',
                    ],
                    [
                        'name' => 'Garden & Outdoor',
                        'slug' => 'garden-outdoor',
                        'description' => 'Plants, tools, outdoor furniture, and more',
                        'icon' => 'tree',
                    ],
                ]
            ],
            [
                'name' => 'Collectibles & Art',
                'slug' => 'collectibles-art',
                'description' => 'Antiques, art, coins, stamps, and collectible items',
                'icon' => 'palette',
                'is_active' => true,
                'sort_order' => 4,
                'children' => [
                    [
                        'name' => 'Antiques',
                        'slug' => 'antiques',
                        'description' => 'Vintage and antique items',
                        'icon' => 'clock',
                    ],
                    [
                        'name' => 'Art & Paintings',
                        'slug' => 'art-paintings',
                        'description' => 'Original art, prints, and paintings',
                        'icon' => 'palette',
                    ],
                    [
                        'name' => 'Coins & Currency',
                        'slug' => 'coins-currency',
                        'description' => 'Rare coins, bills, and currency',
                        'icon' => 'coins',
                    ],
                    [
                        'name' => 'Stamps',
                        'slug' => 'stamps',
                        'description' => 'Rare and collectible stamps',
                        'icon' => 'stamp',
                    ],
                ]
            ],
            [
                'name' => 'Sports & Recreation',
                'slug' => 'sports-recreation',
                'description' => 'Sports equipment, outdoor gear, and recreational items',
                'icon' => 'football',
                'is_active' => true,
                'sort_order' => 5,
                'children' => [
                    [
                        'name' => 'Exercise Equipment',
                        'slug' => 'exercise-equipment',
                        'description' => 'Gym equipment, weights, and fitness gear',
                        'icon' => 'dumbbell',
                    ],
                    [
                        'name' => 'Outdoor Sports',
                        'slug' => 'outdoor-sports',
                        'description' => 'Camping, hiking, and outdoor sports gear',
                        'icon' => 'mountain',
                    ],
                    [
                        'name' => 'Team Sports',
                        'slug' => 'team-sports',
                        'description' => 'Football, basketball, soccer equipment',
                        'icon' => 'football',
                    ],
                    [
                        'name' => 'Water Sports',
                        'slug' => 'water-sports',
                        'description' => 'Swimming, surfing, and water sports gear',
                        'icon' => 'waves',
                    ],
                ]
            ],
            [
                'name' => 'Automotive',
                'slug' => 'automotive',
                'description' => 'Cars, motorcycles, parts, and accessories',
                'icon' => 'car',
                'is_active' => true,
                'sort_order' => 6,
                'children' => [
                    [
                        'name' => 'Cars',
                        'slug' => 'cars',
                        'description' => 'Used and classic cars',
                        'icon' => 'car',
                    ],
                    [
                        'name' => 'Motorcycles',
                        'slug' => 'motorcycles',
                        'description' => 'Motorcycles and scooters',
                        'icon' => 'motorcycle',
                    ],
                    [
                        'name' => 'Parts & Accessories',
                        'slug' => 'parts-accessories',
                        'description' => 'Car parts, tools, and accessories',
                        'icon' => 'wrench',
                    ],
                ]
            ],
            [
                'name' => 'Books & Media',
                'slug' => 'books-media',
                'description' => 'Books, movies, music, and educational materials',
                'icon' => 'book',
                'is_active' => true,
                'sort_order' => 7,
                'children' => [
                    [
                        'name' => 'Books',
                        'slug' => 'books',
                        'description' => 'Fiction, non-fiction, and rare books',
                        'icon' => 'book',
                    ],
                    [
                        'name' => 'Movies & TV',
                        'slug' => 'movies-tv',
                        'description' => 'DVDs, Blu-rays, and collectible media',
                        'icon' => 'film',
                    ],
                    [
                        'name' => 'Music',
                        'slug' => 'music',
                        'description' => 'Vinyl records, CDs, and music memorabilia',
                        'icon' => 'music',
                    ],
                ]
            ],
        ];

        foreach ($categories as $categoryData) {
            $children = $categoryData['children'] ?? [];
            unset($categoryData['children']);

            $category = Category::create($categoryData);

            foreach ($children as $childData) {
                $childData['parent_id'] = $category->id;
                $childData['is_active'] = true;
                $childData['sort_order'] = 1;
                Category::create($childData);
            }
        }
    }
}

<?php

namespace Database\Seeders;

use App\Models\Auction;
use App\Models\Payment;
use Illuminate\Database\Seeder;

class PaymentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $endedAuctions = Auction::where('status', 'ended')
            ->whereNotNull('winner_id')
            ->whereNotNull('final_price')
            ->get();
        
        if ($endedAuctions->isEmpty()) {
            $this->command->warn('No ended auctions with winners found. Please run AuctionSeeder and BidSeeder first.');
            return;
        }

        $paymentMethods = ['stripe', 'paypal', 'bank_transfer'];
        $statuses = ['completed', 'pending', 'failed', 'processing'];
        $totalPayments = 0;

        foreach ($endedAuctions as $auction) {
            // 80% chance of creating a payment for each ended auction
            if (rand(1, 10) <= 8) {
                $paymentMethod = $paymentMethods[array_rand($paymentMethods)];
                $status = $statuses[array_rand($statuses)];
                
                // Adjust status probabilities (more completed payments)
                if (rand(1, 10) <= 7) {
                    $status = 'completed';
                } elseif (rand(1, 10) <= 2) {
                    $status = 'pending';
                } elseif (rand(1, 10) <= 1) {
                    $status = 'failed';
                }

                $amount = $auction->final_price;
                $fees = $amount * 0.05; // 5% platform fee
                $netAmount = $amount - $fees;

                // Generate payment reference
                $reference = strtoupper(substr($paymentMethod, 0, 3)) . '_' . 
                           date('Ymd') . '_' . 
                           str_pad($auction->id, 6, '0', STR_PAD_LEFT) . '_' . 
                           rand(1000, 9999);

                $paymentData = [
                    'user_id' => $auction->winner_id,
                    'auction_id' => $auction->id,
                    'amount' => $amount,
                    'fees' => $fees,
                    'net_amount' => $netAmount,
                    'currency' => 'USD',
                    'payment_method' => $paymentMethod,
                    'status' => $status,
                    'reference' => $reference,
                    'description' => "Payment for auction: {$auction->title}",
                ];

                // Add status-specific data
                switch ($status) {
                    case 'completed':
                        $paymentData['processed_at'] = now()->subDays(rand(1, 30));
                        $paymentData['gateway_transaction_id'] = $this->generateTransactionId($paymentMethod);
                        $paymentData['gateway_response'] = json_encode([
                            'status' => 'success',
                            'transaction_id' => $paymentData['gateway_transaction_id'],
                            'message' => 'Payment completed successfully',
                        ]);
                        break;

                    case 'pending':
                        $paymentData['initiated_at'] = now()->subHours(rand(1, 48));
                        $paymentData['gateway_response'] = json_encode([
                            'status' => 'pending',
                            'message' => 'Payment is being processed',
                        ]);
                        break;

                    case 'failed':
                        $paymentData['failed_at'] = now()->subDays(rand(1, 7));
                        $paymentData['failure_reason'] = $this->getRandomFailureReason();
                        $paymentData['gateway_response'] = json_encode([
                            'status' => 'failed',
                            'error_code' => 'PAYMENT_DECLINED',
                            'message' => $paymentData['failure_reason'],
                        ]);
                        break;

                    case 'processing':
                        $paymentData['initiated_at'] = now()->subMinutes(rand(5, 120));
                        $paymentData['gateway_response'] = json_encode([
                            'status' => 'processing',
                            'message' => 'Payment is being processed by the gateway',
                        ]);
                        break;
                }

                // Set metadata based on payment method
                $metadata = [
                    'auction_title' => $auction->title,
                    'seller_id' => $auction->user_id,
                    'payment_method_details' => $this->getPaymentMethodDetails($paymentMethod),
                ];

                $paymentData['metadata'] = json_encode($metadata);

                Payment::create($paymentData);
                $totalPayments++;
            }
        }

        // Create some additional test payments with different scenarios
        $this->createTestPayments();

        $this->command->info("Created {$totalPayments} payments");
    }

    /**
     * Generate transaction ID based on payment method
     */
    private function generateTransactionId(string $paymentMethod): string
    {
        switch ($paymentMethod) {
            case 'stripe':
                return 'ch_' . str_random(24);
            case 'paypal':
                return 'PAY-' . str_random(17);
            case 'bank_transfer':
                return 'BT' . date('Ymd') . rand(100000, 999999);
            default:
                return 'TXN_' . str_random(16);
        }
    }

    /**
     * Get random failure reason
     */
    private function getRandomFailureReason(): string
    {
        $reasons = [
            'Insufficient funds',
            'Card declined by issuer',
            'Invalid card number',
            'Card expired',
            'Payment method not supported',
            'Transaction limit exceeded',
            'Fraud prevention triggered',
            'Network timeout',
        ];

        return $reasons[array_rand($reasons)];
    }

    /**
     * Get payment method specific details
     */
    private function getPaymentMethodDetails(string $paymentMethod): array
    {
        switch ($paymentMethod) {
            case 'stripe':
                return [
                    'card_last4' => str_pad(rand(1000, 9999), 4, '0', STR_PAD_LEFT),
                    'card_brand' => ['visa', 'mastercard', 'amex'][array_rand(['visa', 'mastercard', 'amex'])],
                    'card_country' => 'US',
                ];
            case 'paypal':
                return [
                    'payer_email' => 'payer' . rand(1000, 9999) . '@example.com',
                    'payer_id' => 'PAYER' . str_random(13),
                ];
            case 'bank_transfer':
                return [
                    'bank_name' => ['Chase Bank', 'Bank of America', 'Wells Fargo'][array_rand(['Chase Bank', 'Bank of America', 'Wells Fargo'])],
                    'account_last4' => str_pad(rand(1000, 9999), 4, '0', STR_PAD_LEFT),
                ];
            default:
                return [];
        }
    }

    /**
     * Create additional test payments for various scenarios
     */
    private function createTestPayments(): void
    {
        // Create a stuck payment (processing for too long)
        $auction = Auction::where('status', 'ended')->whereNotNull('winner_id')->first();
        if ($auction) {
            Payment::create([
                'user_id' => $auction->winner_id,
                'auction_id' => $auction->id,
                'amount' => $auction->final_price,
                'fees' => $auction->final_price * 0.05,
                'net_amount' => $auction->final_price * 0.95,
                'currency' => 'USD',
                'payment_method' => 'stripe',
                'status' => 'processing',
                'reference' => 'STR_' . date('Ymd') . '_STUCK_001',
                'description' => "Stuck payment for auction: {$auction->title}",
                'initiated_at' => now()->subHours(2), // Stuck for 2 hours
                'metadata' => json_encode([
                    'auction_title' => $auction->title,
                    'test_scenario' => 'stuck_payment',
                ]),
            ]);
        }

        // Create a refunded payment
        $auction = Auction::where('status', 'ended')->whereNotNull('winner_id')->skip(1)->first();
        if ($auction) {
            Payment::create([
                'user_id' => $auction->winner_id,
                'auction_id' => $auction->id,
                'amount' => $auction->final_price,
                'fees' => $auction->final_price * 0.05,
                'net_amount' => $auction->final_price * 0.95,
                'currency' => 'USD',
                'payment_method' => 'paypal',
                'status' => 'refunded',
                'reference' => 'PAY_' . date('Ymd') . '_REFUND_001',
                'description' => "Refunded payment for auction: {$auction->title}",
                'processed_at' => now()->subDays(5),
                'refunded_at' => now()->subDays(2),
                'refund_reason' => 'Item not as described',
                'gateway_transaction_id' => 'PAY-' . str_random(17),
                'metadata' => json_encode([
                    'auction_title' => $auction->title,
                    'test_scenario' => 'refunded_payment',
                ]),
            ]);
        }
    }
}

// Helper function for older Laravel versions
if (!function_exists('str_random')) {
    function str_random($length = 16) {
        return \Illuminate\Support\Str::random($length);
    }
}

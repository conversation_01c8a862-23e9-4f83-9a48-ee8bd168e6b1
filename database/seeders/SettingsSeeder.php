<?php

namespace Database\Seeders;

use App\Models\Setting;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SettingsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $settings = [
            [
                'key' => 'allow_user_auction_creation',
                'value' => '1',
                'type' => 'boolean',
                'description' => 'Allow regular users to create auctions. When disabled, only admins can create auctions.',
                'is_public' => true,
            ],
            [
                'key' => 'site_name',
                'value' => 'Auction Platform',
                'type' => 'string',
                'description' => 'The name of the auction site displayed in the header and emails.',
                'is_public' => true,
            ],
            [
                'key' => 'max_auction_duration_days',
                'value' => '30',
                'type' => 'integer',
                'description' => 'Maximum duration for auctions in days.',
                'is_public' => true,
            ],
            [
                'key' => 'min_auction_duration_minutes',
                'value' => '30',
                'type' => 'integer',
                'description' => 'Minimum duration for auctions in minutes.',
                'is_public' => true,
            ],
            [
                'key' => 'require_email_verification_for_bidding',
                'value' => '1',
                'type' => 'boolean',
                'description' => 'Require users to verify their email before they can place bids.',
                'is_public' => true,
            ],
            [
                'key' => 'enable_auction_auto_extend',
                'value' => '1',
                'type' => 'boolean',
                'description' => 'Allow auctions to automatically extend when bids are placed near the end time.',
                'is_public' => true,
            ],
            [
                'key' => 'default_bid_increment',
                'value' => '1.00',
                'type' => 'float',
                'description' => 'Default bid increment amount in the site currency.',
                'is_public' => true,
            ],
            [
                'key' => 'maintenance_mode',
                'value' => '0',
                'type' => 'boolean',
                'description' => 'Enable maintenance mode to prevent new auctions and bids.',
                'is_public' => false,
            ],
        ];

        foreach ($settings as $setting) {
            Setting::updateOrCreate(
                ['key' => $setting['key']],
                $setting
            );
        }
    }
}

<?php

namespace Database\Seeders;

use App\Models\Auction;
use App\Models\Category;
use App\Models\User;
use Illuminate\Database\Seeder;

class AuctionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $sellers = User::whereIn('role', ['seller', 'premium_seller'])->get();
        $categories = Category::whereNotNull('parent_id')->get(); // Only subcategories
        
        if ($sellers->isEmpty() || $categories->isEmpty()) {
            $this->command->warn('No sellers or categories found. Please run UserSeeder and CategorySeeder first.');
            return;
        }

        $auctionData = [
            // Electronics
            [
                'title' => 'iPhone 14 Pro Max 256GB - Space Black',
                'description' => 'Brand new iPhone 14 Pro Max in Space Black. 256GB storage, unlocked, comes with original box and accessories. Never used, still in plastic wrap.',
                'condition' => 'new',
                'starting_price' => 899.00,
                'reserve_price' => 1100.00,
                'buyout_price' => 1299.00,
                'category_name' => 'Smartphones',
                'duration_hours' => 168, // 7 days
                'featured' => true,
            ],
            [
                'title' => 'MacBook Pro 16" M2 Pro - Silver',
                'description' => 'MacBook Pro 16-inch with M2 Pro chip, 16GB RAM, 512GB SSD. Excellent condition, used for 6 months. Includes original charger and box.',
                'condition' => 'like_new',
                'starting_price' => 1800.00,
                'reserve_price' => 2200.00,
                'buyout_price' => 2599.00,
                'category_name' => 'Laptops & Computers',
                'duration_hours' => 120, // 5 days
                'featured' => true,
            ],
            [
                'title' => 'PlayStation 5 Console + 3 Games',
                'description' => 'PlayStation 5 console in excellent condition. Includes 3 popular games: Spider-Man Miles Morales, God of War Ragnarök, and Horizon Forbidden West.',
                'condition' => 'good',
                'starting_price' => 450.00,
                'reserve_price' => 550.00,
                'buyout_price' => 699.00,
                'category_name' => 'Gaming',
                'duration_hours' => 96, // 4 days
                'featured' => false,
            ],
            [
                'title' => 'Sony WH-1000XM5 Wireless Headphones',
                'description' => 'Premium noise-canceling wireless headphones. Like new condition, used only a few times. Includes carrying case and all accessories.',
                'condition' => 'like_new',
                'starting_price' => 250.00,
                'reserve_price' => 320.00,
                'buyout_price' => 399.00,
                'category_name' => 'Audio & Video',
                'duration_hours' => 72, // 3 days
                'featured' => false,
            ],

            // Fashion & Accessories
            [
                'title' => 'Rolex Submariner Date - Black Dial',
                'description' => 'Authentic Rolex Submariner Date with black dial and bezel. Excellent condition, serviced recently. Includes box and papers.',
                'condition' => 'good',
                'starting_price' => 8500.00,
                'reserve_price' => 12000.00,
                'buyout_price' => 15999.00,
                'category_name' => 'Jewelry & Watches',
                'duration_hours' => 240, // 10 days
                'featured' => true,
            ],
            [
                'title' => 'Louis Vuitton Neverfull MM Monogram',
                'description' => 'Authentic Louis Vuitton Neverfull MM in classic monogram canvas. Good condition with normal wear. Includes dustbag.',
                'condition' => 'good',
                'starting_price' => 800.00,
                'reserve_price' => 1100.00,
                'buyout_price' => 1399.00,
                'category_name' => 'Women\'s Clothing',
                'duration_hours' => 144, // 6 days
                'featured' => false,
            ],
            [
                'title' => 'Nike Air Jordan 1 Retro High OG - Size 10',
                'description' => 'Classic Air Jordan 1 in Chicago colorway. Size 10, good condition with minimal wear. Original box included.',
                'condition' => 'good',
                'starting_price' => 150.00,
                'reserve_price' => 220.00,
                'buyout_price' => 299.00,
                'category_name' => 'Shoes',
                'duration_hours' => 96, // 4 days
                'featured' => false,
            ],

            // Home & Garden
            [
                'title' => 'Mid-Century Modern Dining Table - Walnut',
                'description' => 'Beautiful mid-century modern dining table in walnut wood. Seats 6 people comfortably. Some minor scratches but overall excellent condition.',
                'condition' => 'good',
                'starting_price' => 600.00,
                'reserve_price' => 900.00,
                'buyout_price' => 1299.00,
                'category_name' => 'Furniture',
                'duration_hours' => 168, // 7 days
                'featured' => false,
            ],
            [
                'title' => 'KitchenAid Stand Mixer - Artisan Series',
                'description' => 'KitchenAid Artisan Series 5-quart stand mixer in Empire Red. Like new condition, barely used. Includes dough hook, wire whip, and flat beater.',
                'condition' => 'like_new',
                'starting_price' => 200.00,
                'reserve_price' => 280.00,
                'buyout_price' => 349.00,
                'category_name' => 'Kitchen & Dining',
                'duration_hours' => 120, // 5 days
                'featured' => false,
            ],

            // Collectibles & Art
            [
                'title' => 'Original Oil Painting - Landscape Scene',
                'description' => 'Beautiful original oil painting of a mountain landscape. Signed by artist. Framed and ready to hang. 24" x 36".',
                'condition' => 'good',
                'starting_price' => 300.00,
                'reserve_price' => 500.00,
                'buyout_price' => 799.00,
                'category_name' => 'Art & Paintings',
                'duration_hours' => 192, // 8 days
                'featured' => false,
            ],
            [
                'title' => '1909 VDB Lincoln Cent - AU Condition',
                'description' => 'Rare 1909 VDB Lincoln cent in About Uncirculated condition. Great addition to any coin collection. Authenticated and graded.',
                'condition' => 'good',
                'starting_price' => 400.00,
                'reserve_price' => 600.00,
                'buyout_price' => 899.00,
                'category_name' => 'Coins & Currency',
                'duration_hours' => 168, // 7 days
                'featured' => false,
            ],

            // Sports & Recreation
            [
                'title' => 'Trek Mountain Bike - Full Suspension',
                'description' => 'Trek full suspension mountain bike in excellent condition. 21-speed, 26" wheels, recently serviced. Perfect for trail riding.',
                'condition' => 'good',
                'starting_price' => 800.00,
                'reserve_price' => 1200.00,
                'buyout_price' => 1599.00,
                'category_name' => 'Outdoor Sports',
                'duration_hours' => 144, // 6 days
                'featured' => false,
            ],

            // Automotive
            [
                'title' => '1967 Ford Mustang Fastback - Restored',
                'description' => 'Classic 1967 Ford Mustang Fastback, fully restored. 289 V8 engine, 4-speed manual transmission. Show quality paint and interior.',
                'condition' => 'good',
                'starting_price' => 35000.00,
                'reserve_price' => 45000.00,
                'buyout_price' => 59999.00,
                'category_name' => 'Cars',
                'duration_hours' => 336, // 14 days
                'featured' => true,
            ],

            // Books & Media
            [
                'title' => 'First Edition Harry Potter Philosopher\'s Stone',
                'description' => 'First edition, first printing of Harry Potter and the Philosopher\'s Stone. Excellent condition with dust jacket. A true collector\'s item.',
                'condition' => 'good',
                'starting_price' => 2000.00,
                'reserve_price' => 3500.00,
                'buyout_price' => 4999.00,
                'category_name' => 'Books',
                'duration_hours' => 240, // 10 days
                'featured' => true,
            ],
        ];

        foreach ($auctionData as $data) {
            $category = $categories->where('name', $data['category_name'])->first();
            if (!$category) {
                continue;
            }

            $seller = $sellers->random();
            $startTime = now()->addHours(rand(-24, 48)); // Some started, some starting soon
            $endTime = $startTime->copy()->addHours($data['duration_hours']);

            // Determine status based on timing
            $status = 'scheduled';
            if ($startTime->isPast()) {
                $status = $endTime->isPast() ? 'ended' : 'active';
            }

            $auction = Auction::create([
                'user_id' => $seller->id,
                'category_id' => $category->id,
                'title' => $data['title'],
                'description' => $data['description'],
                'condition' => $data['condition'],
                'starting_price' => $data['starting_price'],
                'current_bid' => $status === 'active' ? $data['starting_price'] + rand(10, 200) : $data['starting_price'],
                'reserve_price' => $data['reserve_price'],
                'buyout_price' => $data['buyout_price'],
                'bid_increment' => max(1.00, $data['starting_price'] * 0.05), // 5% of starting price
                'start_time' => $startTime,
                'end_time' => $endTime,
                'status' => $status,
                'auto_extend' => rand(0, 1),
                'extend_minutes' => rand(0, 1) ? 10 : 0,
                'views_count' => rand(50, 500),
                'watchers_count' => rand(5, 50),
                'bids_count' => $status === 'active' ? rand(1, 25) : 0,
                'featured' => $data['featured'],
                'featured_until' => $data['featured'] ? now()->addDays(7) : null,
            ]);

            // Add some ended auctions with winners
            if ($status === 'ended' && rand(0, 1)) {
                $bidders = User::where('role', 'user')->inRandomOrder()->limit(3)->get();
                if ($bidders->isNotEmpty()) {
                    $winner = $bidders->first();
                    $finalPrice = $auction->current_bid + rand(50, 300);
                    
                    $auction->update([
                        'winner_id' => $winner->id,
                        'final_price' => $finalPrice,
                        'current_bid' => $finalPrice,
                        'actual_end_time' => $endTime,
                    ]);
                }
            }
        }

        $this->command->info('Created ' . count($auctionData) . ' auctions');
    }
}

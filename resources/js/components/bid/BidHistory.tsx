import React, { useState, useEffect } from 'react';
import { Crown, TrendingUp, Clock, User } from 'lucide-react';
import { Bid, PaginatedData } from '../../types';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { cn } from '../../lib/utils';

interface BidHistoryProps {
  auctionId: number;
  initialBids?: PaginatedData<Bid>;
  className?: string;
  showRealTime?: boolean;
}

const BidHistory: React.FC<BidHistoryProps> = ({
  auctionId,
  initialBids,
  className,
  showRealTime = true
}) => {
  const [bids, setBids] = useState<Bid[]>(initialBids?.data || []);
  const [loading, setLoading] = useState(false);

  // Real-time bid updates
  useEffect(() => {
    if (!showRealTime) return;

    const interval = setInterval(async () => {
      try {
        const response = await fetch(`/auctions/${auctionId}/recent-bids?minutes=5`);
        const data = await response.json();

        if (data.data && data.data.length > 0) {
          setBids(prevBids => {
            const newBids = data.data.filter((newBid: Bid) =>
              !prevBids.some(existingBid => existingBid.id === newBid.id)
            );
            return [...newBids, ...prevBids].slice(0, 20); // Keep only latest 20
          });
        }
      } catch (error) {
        console.error('Failed to fetch recent bids:', error);
      }
    }, 5000); // Update every 5 seconds

    return () => clearInterval(interval);
  }, [auctionId, showRealTime]);

  const formatTimeAgo = (timestamp: string): string => {
    const now = new Date().getTime();
    const bidTime = new Date(timestamp).getTime();
    const difference = now - bidTime;

    const minutes = Math.floor(difference / (1000 * 60));
    const hours = Math.floor(difference / (1000 * 60 * 60));
    const days = Math.floor(difference / (1000 * 60 * 60 * 24));

    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    if (minutes > 0) return `${minutes}m ago`;
    return 'Just now';
  };

  const getBidTypeIcon = (bidType: string) => {
    switch (bidType) {
      case 'proxy':
        return <TrendingUp className="h-3 w-3" />;
      case 'auto':
        return <TrendingUp className="h-3 w-3" />;
      default:
        return <User className="h-3 w-3" />;
    }
  };

  const getBidTypeColor = (bidType: string) => {
    switch (bidType) {
      case 'proxy':
        return 'text-blue-600';
      case 'auto':
        return 'text-purple-600';
      default:
        return 'text-gray-600';
    }
  };

  if (!bids.length) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center">
            <TrendingUp className="h-5 w-5 mr-2" />
            Bid History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-gray-600">
            <TrendingUp className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No bids placed yet</p>
            <p className="text-sm">Be the first to bid on this auction!</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center">
            <TrendingUp className="h-5 w-5 mr-2" />
            Bid History
          </div>
          <Badge variant="secondary" className="text-xs">
            {bids.length} bid{bids.length !== 1 ? 's' : ''}
          </Badge>
        </CardTitle>
      </CardHeader>

      <CardContent className="p-0">
        <div className="max-h-96 overflow-y-auto">
          {bids.map((bid, index) => (
            <div
              key={bid.id}
              className={cn(
                'flex items-center justify-between p-4 border-b last:border-b-0',
                index === 0 && bid.status.is_winning && 'bg-green-50 border-green-200',
                'hover:bg-muted/50 transition-colors'
              )}
            >
              <div className="flex items-center space-x-3">
                {/* Winning Badge */}
                {index === 0 && bid.status.is_winning && (
                  <Crown className="h-4 w-4 text-yellow-500 flex-shrink-0" />
                )}

                {/* Bid Type Icon */}
                <div className={cn('flex-shrink-0', getBidTypeColor(bid.bid_type))}>
                  {getBidTypeIcon(bid.bid_type)}
                </div>

                {/* Bidder Info */}
                <div className="min-w-0 flex-1">
                  <div className="flex items-center space-x-2">
                    <span className="font-medium text-sm truncate">
                      {bid.bidder_name || 'Anonymous'}
                    </span>
                    {bid.bid_type !== 'manual' && (
                      <Badge variant="outline" className="text-xs">
                        {bid.bid_type}
                      </Badge>
                    )}
                  </div>
                  <div className="flex items-center text-xs text-muted-foreground">
                    <Clock className="h-3 w-3 mr-1" />
                    {formatTimeAgo(bid.timing.timestamp)}
                  </div>
                </div>
              </div>

              {/* Bid Amount */}
              <div className="text-right">
                <div className={cn(
                  'font-bold',
                  index === 0 && bid.status.is_winning
                    ? 'text-green-600 text-lg'
                    : 'text-foreground'
                )}>
                  {bid.amount.formatted}
                </div>
                {bid.max_bid && bid.bid_type === 'proxy' && (
                  <div className="text-xs text-muted-foreground">
                    Max: {bid.max_bid.formatted}
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Load More Button */}
        {initialBids && initialBids.meta.current_page < initialBids.meta.last_page && (
          <div className="p-4 border-t">
            <Button
              variant="outline"
              size="sm"
              className="w-full"
              onClick={() => {
                // Load more bids logic
                setLoading(true);
                // Implementation would go here
                setLoading(false);
              }}
              disabled={loading}
            >
              {loading ? 'Loading...' : 'Load More Bids'}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default BidHistory;

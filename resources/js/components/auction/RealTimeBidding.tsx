import React, { useState, useEffect, useRef } from 'react';
import { Zap, Users, TrendingUp, Clock, AlertCircle } from 'lucide-react';
import { Auction, Bid } from '../../types';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Badge } from '../ui/badge';
import { Button } from '../ui/button';
import { Alert, AlertDescription } from '../ui/alert';
import { cn } from '../../lib/utils';

interface RealTimeBiddingProps {
  auction: Auction;
  onBidUpdate?: (bid: Bid) => void;
  onAuctionUpdate?: (auction: Auction) => void;
  className?: string;
}

interface WebSocketMessage {
  type: 'bid.placed' | 'auction.updated' | 'auction.ended' | 'countdown.update';
  data: any;
}

const RealTimeBidding: React.FC<RealTimeBiddingProps> = ({
  auction,
  onBidUpdate,
  onAuctionUpdate,
  className
}) => {
  const [isConnected, setIsConnected] = useState(false);
  const [connectionError, setConnectionError] = useState<string | null>(null);
  const [recentBids, setRecentBids] = useState<Bid[]>([]);
  const [activeUsers, setActiveUsers] = useState(0);
  const [timeRemaining, setTimeRemaining] = useState<string>('');
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const countdownIntervalRef = useRef<NodeJS.Timeout | null>(null);

  // Initialize WebSocket connection
  useEffect(() => {
    connectWebSocket();
    startCountdown();

    return () => {
      disconnectWebSocket();
      if (countdownIntervalRef.current) {
        clearInterval(countdownIntervalRef.current);
      }
    };
  }, [auction.id]);

  const connectWebSocket = () => {
    try {
      // In a real implementation, you would use your WebSocket server URL
      // For now, we'll simulate the connection
      const wsUrl = `${window.location.protocol === 'https:' ? 'wss:' : 'ws:'}//${window.location.host}/ws`;
      
      // Simulate WebSocket connection
      setIsConnected(true);
      setConnectionError(null);
      
      // Simulate receiving initial data
      setActiveUsers(Math.floor(Math.random() * 20) + 5);
      
      // In a real implementation:
      // wsRef.current = new WebSocket(wsUrl);
      // wsRef.current.onopen = handleWebSocketOpen;
      // wsRef.current.onmessage = handleWebSocketMessage;
      // wsRef.current.onclose = handleWebSocketClose;
      // wsRef.current.onerror = handleWebSocketError;

    } catch (error) {
      console.error('WebSocket connection failed:', error);
      setConnectionError('Failed to connect to real-time updates');
      scheduleReconnect();
    }
  };

  const disconnectWebSocket = () => {
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
    setIsConnected(false);
  };

  const scheduleReconnect = () => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
    
    reconnectTimeoutRef.current = setTimeout(() => {
      connectWebSocket();
    }, 5000); // Retry after 5 seconds
  };

  const handleWebSocketMessage = (event: MessageEvent) => {
    try {
      const message: WebSocketMessage = JSON.parse(event.data);
      
      switch (message.type) {
        case 'bid.placed':
          const newBid = message.data as Bid;
          setRecentBids(prev => [newBid, ...prev.slice(0, 4)]); // Keep last 5 bids
          onBidUpdate?.(newBid);
          break;
          
        case 'auction.updated':
          const updatedAuction = message.data as Auction;
          onAuctionUpdate?.(updatedAuction);
          break;
          
        case 'auction.ended':
          onAuctionUpdate?.(message.data as Auction);
          break;
          
        case 'countdown.update':
          // Handle countdown updates if needed
          break;
      }
    } catch (error) {
      console.error('Failed to parse WebSocket message:', error);
    }
  };

  const startCountdown = () => {
    const updateCountdown = () => {
      const now = new Date().getTime();
      const endTime = new Date(auction.end_time).getTime();
      const distance = endTime - now;

      if (distance > 0) {
        const days = Math.floor(distance / (1000 * 60 * 60 * 24));
        const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
        const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((distance % (1000 * 60)) / 1000);

        if (days > 0) {
          setTimeRemaining(`${days}d ${hours}h ${minutes}m`);
        } else if (hours > 0) {
          setTimeRemaining(`${hours}h ${minutes}m ${seconds}s`);
        } else {
          setTimeRemaining(`${minutes}m ${seconds}s`);
        }
      } else {
        setTimeRemaining('Auction ended');
        if (countdownIntervalRef.current) {
          clearInterval(countdownIntervalRef.current);
        }
      }
    };

    updateCountdown();
    countdownIntervalRef.current = setInterval(updateCountdown, 1000);
  };

  const formatBidAmount = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount / 100);
  };

  const getTimeRemainingColor = () => {
    const now = new Date().getTime();
    const endTime = new Date(auction.end_time).getTime();
    const distance = endTime - now;
    const hoursRemaining = distance / (1000 * 60 * 60);

    if (hoursRemaining < 1) return 'text-red-600';
    if (hoursRemaining < 24) return 'text-yellow-600';
    return 'text-green-600';
  };

  return (
    <div className={cn('space-y-4', className)}>
      {/* Connection Status */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <div className={cn(
                'w-2 h-2 rounded-full',
                isConnected ? 'bg-green-500' : 'bg-red-500'
              )} />
              <span className="text-sm font-medium">
                {isConnected ? 'Live Updates Active' : 'Disconnected'}
              </span>
              {isConnected && (
                <Badge variant="outline" className="text-xs">
                  <Users className="h-3 w-3 mr-1" />
                  {activeUsers} watching
                </Badge>
              )}
            </div>
            <div className="flex items-center space-x-2">
              <Clock className="h-4 w-4 text-gray-500" />
              <span className={cn('text-sm font-medium', getTimeRemainingColor())}>
                {timeRemaining}
              </span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Connection Error */}
      {connectionError && (
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {connectionError}
            <Button
              variant="link"
              size="sm"
              onClick={connectWebSocket}
              className="ml-2 p-0 h-auto"
            >
              Retry
            </Button>
          </AlertDescription>
        </Alert>
      )}

      {/* Recent Bids */}
      {recentBids.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="text-lg flex items-center">
              <TrendingUp className="h-5 w-5 mr-2" />
              Recent Bids
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {recentBids.map((bid, index) => (
                <div
                  key={bid.id}
                  className={cn(
                    'flex items-center justify-between p-3 rounded-lg border',
                    index === 0 ? 'bg-green-50 border-green-200' : 'bg-gray-50'
                  )}
                >
                  <div className="flex items-center space-x-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-sm font-medium text-blue-600">
                        {bid.bidder_name?.charAt(0).toUpperCase() || 'A'}
                      </span>
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">
                        {bid.bidder_name || 'Anonymous'}
                      </div>
                      <div className="text-sm text-gray-500">
                        {new Date(bid.created_at).toLocaleTimeString()}
                      </div>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="font-bold text-lg text-gray-900">
                      {formatBidAmount(bid.amount, auction.current_bid.currency)}
                    </div>
                    {bid.bid_type === 'proxy' && (
                      <Badge variant="outline" className="text-xs">
                        Proxy
                      </Badge>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Auction Activity Indicator */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Zap className="h-4 w-4 text-yellow-500" />
              <span className="text-sm font-medium">Auction Activity</span>
            </div>
            <div className="flex items-center space-x-4 text-sm text-gray-600">
              <span>{auction.bids_count} bids</span>
              <span>{auction.watchers_count} watchers</span>
              <span>{auction.views_count} views</span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default RealTimeBidding;

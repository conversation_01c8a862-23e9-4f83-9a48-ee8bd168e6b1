import React, { useState } from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import {
  Grid,
  List,
  Search,
  Filter,
  ChevronRight,
  Package,
  TrendingUp,
  Eye,
  Gavel
} from 'lucide-react';
import { PageProps } from '../../types';
import AppLayout from '../../layouts/app-layout';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Badge } from '../../components/ui/badge';
import { <PERSON><PERSON>, TabsContent, TabsList, TabsTrigger } from '../../components/ui/tabs';
import { cn } from '../../lib/utils';

interface Category {
  id: number;
  name: string;
  slug: string;
  description?: string;
  image_url?: string;
  icon?: string;
  parent_id?: number;
  children?: Category[];
  auctions_count?: number;
  active_auctions_count?: number;
  is_featured: boolean;
  metadata: {
    is_root: boolean;
    has_image: boolean;
    has_icon: boolean;
    is_popular?: boolean;
  };
}

interface Props extends PageProps {
  categories: Category[] | { data: Category[] };
  featured_categories?: Category[] | { data: Category[] };
  popular_categories?: Category[] | { data: Category[] };
  search?: string;
}

const CategoriesIndex: React.FC<Props> = ({
  categories,
  featured_categories = [],
  popular_categories = [],
  search
}) => {
  const [searchTerm, setSearchTerm] = useState(search || '');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [selectedParent, setSelectedParent] = useState<number | null>(null);

  // Extract categories array from resource collection or use as-is if already an array
  const categoriesArray = Array.isArray(categories) ? categories : categories.data || [];
  const featuredCategoriesArray = Array.isArray(featured_categories) ? featured_categories : featured_categories?.data || [];
  const popularCategoriesArray = Array.isArray(popular_categories) ? popular_categories : popular_categories?.data || [];

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // In a real app, you'd navigate with the search term
    window.location.href = `/categories?search=${encodeURIComponent(searchTerm)}`;
  };

  const rootCategories = categoriesArray.filter((cat: Category) => cat.metadata.is_root);
  const filteredCategories = selectedParent
    ? categoriesArray.filter((cat: Category) => cat.parent_id === selectedParent)
    : rootCategories;

  const getCategoryIcon = (category: Category) => {
    if (category.metadata.has_icon && category.icon) {
      return (
        <div className="w-12 h-12 flex items-center justify-center text-2xl">
          {category.icon}
        </div>
      );
    }
    return <Package className="w-12 h-12 text-gray-400" />;
  };

  const CategoryCard: React.FC<{ category: Category }> = ({ category }) => (
    <Card className="group hover:shadow-lg transition-all duration-200 hover:-translate-y-1">
      <CardContent className="p-6">
        <Link href={`/categories/${category.slug}`} className="block">
          <div className="flex items-start space-x-4">
            <div className="flex-shrink-0">
              {category.metadata.has_image && category.image_url ? (
                <img
                  src={category.image_url}
                  alt={category.name}
                  className="w-12 h-12 rounded-lg object-cover"
                />
              ) : (
                getCategoryIcon(category)
              )}
            </div>

            <div className="flex-1 min-w-0">
              <div className="flex items-center justify-between mb-2">
                <h3 className="text-lg font-semibold text-gray-900 group-hover:text-primary truncate">
                  {category.name}
                </h3>
                {category.is_featured && (
                  <Badge variant="default" className="ml-2">
                    Featured
                  </Badge>
                )}
              </div>

              {category.description && (
                <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                  {category.description}
                </p>
              )}

              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-4 text-sm text-gray-500">
                  {category.active_auctions_count !== undefined && (
                    <div className="flex items-center">
                      <Gavel className="w-4 h-4 mr-1" />
                      {category.active_auctions_count} active
                    </div>
                  )}
                  {category.auctions_count !== undefined && (
                    <div className="flex items-center">
                      <Package className="w-4 h-4 mr-1" />
                      {category.auctions_count} total
                    </div>
                  )}
                </div>

                {category.children && category.children.length > 0 && (
                  <div className="flex items-center text-sm text-gray-500">
                    <span>{category.children.length} subcategories</span>
                    <ChevronRight className="w-4 h-4 ml-1" />
                  </div>
                )}
              </div>
            </div>
          </div>
        </Link>
      </CardContent>
    </Card>
  );

  const CategoryListItem: React.FC<{ category: Category }> = ({ category }) => (
    <div className="border-b border-gray-200 last:border-b-0">
      <Link
        href={`/categories/${category.slug}`}
        className="block p-4 hover:bg-gray-50 transition-colors"
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex-shrink-0">
              {category.metadata.has_image && category.image_url ? (
                <img
                  src={category.image_url}
                  alt={category.name}
                  className="w-10 h-10 rounded object-cover"
                />
              ) : (
                <div className="w-10 h-10 bg-gray-100 rounded flex items-center justify-center">
                  <Package className="w-5 h-5 text-gray-400" />
                </div>
              )}
            </div>

            <div>
              <div className="flex items-center space-x-2">
                <h3 className="font-medium text-gray-900">{category.name}</h3>
                {category.is_featured && (
                  <Badge variant="default" className="text-xs">
                    Featured
                  </Badge>
                )}
              </div>
              {category.description && (
                <p className="text-sm text-gray-600 mt-1 line-clamp-1">
                  {category.description}
                </p>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-6 text-sm text-gray-500">
            {category.active_auctions_count !== undefined && (
              <div className="text-center">
                <div className="font-medium text-gray-900">
                  {category.active_auctions_count}
                </div>
                <div>Active</div>
              </div>
            )}
            {category.auctions_count !== undefined && (
              <div className="text-center">
                <div className="font-medium text-gray-900">
                  {category.auctions_count}
                </div>
                <div>Total</div>
              </div>
            )}
            <ChevronRight className="w-5 h-5" />
          </div>
        </div>
      </Link>
    </div>
  );

  return (
    <AppLayout>
      <Head title="Categories" />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-4">Browse Categories</h1>
          <p className="text-gray-600">
            Discover auctions organized by category. Find exactly what you're looking for.
          </p>
        </div>

        {/* Search and Filters */}
        <div className="mb-8">
          <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
            <form onSubmit={handleSearch} className="flex-1 max-w-md">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  type="text"
                  placeholder="Search categories..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </form>

            <div className="flex items-center space-x-2">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                <Grid className="w-4 h-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                <List className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Featured Categories */}
        {featuredCategoriesArray.length > 0 && (
          <div className="mb-12">
            <h2 className="text-2xl font-bold text-gray-900 mb-6">Featured Categories</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {featuredCategoriesArray.map((category: Category) => (
                <CategoryCard key={category.id} category={category} />
              ))}
            </div>
          </div>
        )}

        {/* Main Categories */}
        <Tabs defaultValue="all" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="all">All Categories</TabsTrigger>
            <TabsTrigger value="popular">Popular</TabsTrigger>
            <TabsTrigger value="recent">Recently Added</TabsTrigger>
          </TabsList>

          <TabsContent value="all" className="mt-6">
            {viewMode === 'grid' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {filteredCategories.map((category) => (
                  <CategoryCard key={category.id} category={category} />
                ))}
              </div>
            ) : (
              <Card>
                <CardContent className="p-0">
                  {filteredCategories.map((category) => (
                    <CategoryListItem key={category.id} category={category} />
                  ))}
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="popular" className="mt-6">
            {viewMode === 'grid' ? (
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {popularCategoriesArray.map((category: Category) => (
                  <CategoryCard key={category.id} category={category} />
                ))}
              </div>
            ) : (
              <Card>
                <CardContent className="p-0">
                  {popularCategoriesArray.map((category: Category) => (
                    <CategoryListItem key={category.id} category={category} />
                  ))}
                </CardContent>
              </Card>
            )}
          </TabsContent>

          <TabsContent value="recent" className="mt-6">
            <div className="text-center py-12">
              <Package className="mx-auto h-12 w-12 text-gray-400 mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">Coming Soon</h3>
              <p className="text-gray-600">Recently added categories will appear here.</p>
            </div>
          </TabsContent>
        </Tabs>

        {/* Empty State */}
        {filteredCategories.length === 0 && (
          <div className="text-center py-12">
            <Package className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No categories found</h3>
            <p className="text-gray-600">
              {searchTerm
                ? `No categories match "${searchTerm}". Try a different search term.`
                : 'No categories are available at the moment.'
              }
            </p>
          </div>
        )}
      </div>
    </AppLayout>
  );
};

export default CategoriesIndex;

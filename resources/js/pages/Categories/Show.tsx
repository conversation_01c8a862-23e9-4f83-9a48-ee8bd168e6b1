import React, { useState } from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import {
  ChevronRight,
  Grid,
  List,
  Filter,
  SortAsc,
  Package,
  Gavel,
  Clock,
  TrendingUp
} from 'lucide-react';
import { PageProps, Auction } from '../../types';
import AppLayout from '../../layouts/app-layout';
import AuctionCard from '../../components/auction/AuctionCard';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Badge } from '../../components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../components/ui/select';
import { cn } from '../../lib/utils';

interface Category {
  id: number;
  name: string;
  slug: string;
  description?: string;
  image_url?: string;
  icon?: string;
  parent_id?: number;
  children?: Category[];
  auctions_count?: number;
  active_auctions_count?: number;
  is_featured: boolean;
  path?: Array<{
    id: number;
    name: string;
    slug: string;
    url: string;
  }>;
}

interface Props extends PageProps {
  category: Category;
  auctions: {
    data: Auction[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
  };
  subcategories: Category[];
  filters: {
    sort?: string;
    status?: string;
    price_min?: number;
    price_max?: number;
  };
}

const CategoryShow: React.FC<Props> = ({
  category,
  auctions,
  subcategories,
  filters
}) => {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState(filters.sort || 'ending_soon');
  const [statusFilter, setStatusFilter] = useState(filters.status || 'all');

  const handleFilterChange = (key: string, value: string) => {
    const params = new URLSearchParams(window.location.search);
    if (value === 'all' || value === '') {
      params.delete(key);
    } else {
      params.set(key, value);
    }
    
    const newUrl = `${window.location.pathname}?${params.toString()}`;
    window.location.href = newUrl;
  };

  const getCategoryIcon = (cat: Category) => {
    if (cat.icon) {
      return (
        <div className="w-8 h-8 flex items-center justify-center text-lg">
          {cat.icon}
        </div>
      );
    }
    return <Package className="w-8 h-8 text-gray-400" />;
  };

  return (
    <AppLayout>
      <Head title={`${category.name} - Categories`} />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Breadcrumb */}
        {category.path && (
          <nav className="mb-6">
            <div className="flex items-center space-x-2 text-sm text-muted-foreground">
              <Link href="/categories" className="hover:text-primary">Categories</Link>
              {category.path.map((item, index) => (
                <React.Fragment key={item.id}>
                  <ChevronRight className="h-4 w-4" />
                  <Link
                    href={item.url}
                    className={cn(
                      'hover:text-primary',
                      index === category.path!.length - 1 && 'text-foreground font-medium'
                    )}
                  >
                    {item.name}
                  </Link>
                </React.Fragment>
              ))}
            </div>
          </nav>
        )}

        {/* Category Header */}
        <div className="mb-8">
          <div className="flex items-start space-x-6">
            {category.image_url ? (
              <img
                src={category.image_url}
                alt={category.name}
                className="w-24 h-24 rounded-lg object-cover"
              />
            ) : (
              <div className="w-24 h-24 bg-gray-100 rounded-lg flex items-center justify-center">
                {getCategoryIcon(category)}
              </div>
            )}
            
            <div className="flex-1">
              <div className="flex items-center space-x-3 mb-2">
                <h1 className="text-3xl font-bold text-gray-900">{category.name}</h1>
                {category.is_featured && (
                  <Badge variant="default">Featured</Badge>
                )}
              </div>
              
              {category.description && (
                <p className="text-gray-600 mb-4">{category.description}</p>
              )}
              
              <div className="flex items-center space-x-6 text-sm text-gray-500">
                {category.active_auctions_count !== undefined && (
                  <div className="flex items-center">
                    <Gavel className="w-4 h-4 mr-1" />
                    {category.active_auctions_count} active auctions
                  </div>
                )}
                {category.auctions_count !== undefined && (
                  <div className="flex items-center">
                    <Package className="w-4 h-4 mr-1" />
                    {category.auctions_count} total auctions
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Subcategories */}
        {subcategories.length > 0 && (
          <div className="mb-8">
            <h2 className="text-xl font-semibold text-gray-900 mb-4">Subcategories</h2>
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
              {subcategories.map((subcategory) => (
                <Link
                  key={subcategory.id}
                  href={`/categories/${subcategory.slug}`}
                  className="group"
                >
                  <Card className="hover:shadow-md transition-shadow">
                    <CardContent className="p-4 text-center">
                      <div className="mb-2">
                        {subcategory.image_url ? (
                          <img
                            src={subcategory.image_url}
                            alt={subcategory.name}
                            className="w-12 h-12 mx-auto rounded object-cover"
                          />
                        ) : (
                          <div className="w-12 h-12 mx-auto bg-gray-100 rounded flex items-center justify-center">
                            {getCategoryIcon(subcategory)}
                          </div>
                        )}
                      </div>
                      <h3 className="font-medium text-sm text-gray-900 group-hover:text-primary">
                        {subcategory.name}
                      </h3>
                      {subcategory.active_auctions_count !== undefined && (
                        <p className="text-xs text-gray-500 mt-1">
                          {subcategory.active_auctions_count} auctions
                        </p>
                      )}
                    </CardContent>
                  </Card>
                </Link>
              ))}
            </div>
          </div>
        )}

        {/* Filters and Controls */}
        <div className="mb-6">
          <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
            <div className="flex items-center space-x-4">
              <Select value={sortBy} onValueChange={(value) => {
                setSortBy(value);
                handleFilterChange('sort', value);
              }}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ending_soon">Ending Soon</SelectItem>
                  <SelectItem value="newest">Newest First</SelectItem>
                  <SelectItem value="price_low">Price: Low to High</SelectItem>
                  <SelectItem value="price_high">Price: High to Low</SelectItem>
                  <SelectItem value="most_bids">Most Bids</SelectItem>
                  <SelectItem value="most_watched">Most Watched</SelectItem>
                </SelectContent>
              </Select>

              <Select value={statusFilter} onValueChange={(value) => {
                setStatusFilter(value);
                handleFilterChange('status', value);
              }}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="ending_soon">Ending Soon</SelectItem>
                  <SelectItem value="scheduled">Upcoming</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">
                {auctions.total} auctions
              </span>
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                <Grid className="w-4 h-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                <List className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Auctions Grid/List */}
        {auctions.data.length > 0 ? (
          <div className={cn(
            viewMode === 'grid'
              ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
              : 'space-y-4'
          )}>
            {auctions.data.map((auction) => (
              <AuctionCard
                key={auction.id}
                auction={auction}
                variant={viewMode === 'list' ? 'horizontal' : 'vertical'}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <Gavel className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No auctions found</h3>
            <p className="text-gray-600 mb-4">
              There are currently no auctions in this category.
            </p>
            <Button asChild>
              <Link href="/auctions">Browse All Auctions</Link>
            </Button>
          </div>
        )}

        {/* Pagination */}
        {auctions.last_page > 1 && (
          <div className="mt-8 flex items-center justify-center space-x-2">
            {Array.from({ length: auctions.last_page }, (_, i) => i + 1).map((page) => (
              <Button
                key={page}
                variant={page === auctions.current_page ? 'default' : 'outline'}
                size="sm"
                onClick={() => {
                  const params = new URLSearchParams(window.location.search);
                  params.set('page', page.toString());
                  window.location.href = `${window.location.pathname}?${params.toString()}`;
                }}
              >
                {page}
              </Button>
            ))}
          </div>
        )}
      </div>
    </AppLayout>
  );
};

export default CategoryShow;

import React, { useState } from 'react';
import { Head } from '@inertiajs/react';
import {
  Grid,
  List,
  Filter,
  SortAsc,
  G<PERSON><PERSON>,
  Clock,
  TrendingUp
} from 'lucide-react';
import { PageProps, Auction } from '../../types';
import AppLayout from '../../layouts/app-layout';
import AuctionCard from '../../components/auction/AuctionCard';
import { Button } from '../../components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../components/ui/select';
import { cn } from '../../lib/utils';

interface Category {
  id: number;
  name: string;
  slug: string;
  description?: string;
  image_url?: string;
  icon?: string;
  auctions_count?: number;
  active_auctions_count?: number;
}

interface Props extends PageProps {
  category: Category;
  auctions: {
    data: Auction[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
  };
  filters: {
    status?: string;
    search?: string;
    sort_by?: string;
    per_page?: number;
  };
}

const CategoryAuctions: React.FC<Props> = ({ category, auctions, filters }) => {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState(filters.sort_by || 'ending_soon');
  const [statusFilter, setStatusFilter] = useState(filters.status || 'all');

  const handleFilterChange = (key: string, value: string) => {
    const params = new URLSearchParams(window.location.search);
    if (value === 'all' || value === '') {
      params.delete(key);
    } else {
      params.set(key, value);
    }

    const newUrl = `${window.location.pathname}?${params.toString()}`;
    window.location.href = newUrl;
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'ending_soon':
        return 'bg-yellow-100 text-yellow-800';
      case 'scheduled':
        return 'bg-blue-100 text-blue-800';
      case 'ended':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <AppLayout>
      <Head title={`${category.name} Auctions`} />

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">{category.name} Auctions</h1>
          <p className="text-gray-600 mt-2">
            {category.description || `Browse auctions in the ${category.name} category with advanced filtering options.`}
          </p>
        </div>

        {/* Filters and Controls */}
        <div className="mb-6">
          <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
            <div className="flex items-center space-x-4">
              <Select value={sortBy} onValueChange={(value) => {
                setSortBy(value);
                handleFilterChange('sort_by', value);
              }}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ending_soon">Ending Soon</SelectItem>
                  <SelectItem value="newest">Newest First</SelectItem>
                  <SelectItem value="price_low">Price: Low to High</SelectItem>
                  <SelectItem value="price_high">Price: High to Low</SelectItem>
                  <SelectItem value="most_bids">Most Bids</SelectItem>
                  <SelectItem value="most_watched">Most Watched</SelectItem>
                  <SelectItem value="recently_updated">Recently Updated</SelectItem>
                </SelectContent>
              </Select>

              <Select value={statusFilter} onValueChange={(value) => {
                setStatusFilter(value);
                handleFilterChange('status', value);
              }}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="active">Active</SelectItem>
                  <SelectItem value="ending_soon">Ending Soon</SelectItem>
                  <SelectItem value="scheduled">Upcoming</SelectItem>
                  <SelectItem value="ended">Ended</SelectItem>
                </SelectContent>
              </Select>

              <Button variant="outline" size="sm">
                <Filter className="h-4 w-4 mr-2" />
                More Filters
              </Button>
            </div>

            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600">
                {auctions.total} auctions found
              </span>

              <div className="flex items-center space-x-2">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                >
                  <Grid className="w-4 h-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'outline'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                >
                  <List className="w-4 h-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
          <div className="bg-white p-4 rounded-lg border">
            <div className="flex items-center space-x-2">
              <Gavel className="h-5 w-5 text-blue-600" />
              <div>
                <div className="text-2xl font-bold text-gray-900">
                  {auctions.data.filter(a => a.status === 'active').length}
                </div>
                <div className="text-sm text-gray-600">Active Auctions</div>
              </div>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border">
            <div className="flex items-center space-x-2">
              <Clock className="h-5 w-5 text-yellow-600" />
              <div>
                <div className="text-2xl font-bold text-gray-900">
                  {auctions.data.filter(a => a.timing?.is_ending_soon).length}
                </div>
                <div className="text-sm text-gray-600">Ending Soon</div>
              </div>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-5 w-5 text-green-600" />
              <div>
                <div className="text-2xl font-bold text-gray-900">
                  {auctions.data.reduce((sum, a) => sum + (a.statistics?.bids_count || 0), 0)}
                </div>
                <div className="text-sm text-gray-600">Total Bids</div>
              </div>
            </div>
          </div>

          <div className="bg-white p-4 rounded-lg border">
            <div className="flex items-center space-x-2">
              <div className="w-5 h-5 bg-purple-600 rounded"></div>
              <div>
                <div className="text-2xl font-bold text-gray-900">
                  {auctions.data.filter(a => a.features?.is_featured).length}
                </div>
                <div className="text-sm text-gray-600">Featured</div>
              </div>
            </div>
          </div>
        </div>

        {/* Auctions Grid/List */}
        {auctions.data.length > 0 ? (
          <div className={cn(
            viewMode === 'grid'
              ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
              : 'space-y-4'
          )}>
            {auctions.data.map((auction) => (
              <AuctionCard
                key={auction.id}
                auction={auction}
                variant={viewMode === 'list' ? 'horizontal' : 'vertical'}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <Gavel className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No auctions found</h3>
            <p className="text-gray-600 mb-4">
              No auctions match your current filters. Try adjusting your search criteria.
            </p>
            <div className="space-x-2">
              <Button
                variant="outline"
                onClick={() => {
                  setSortBy('ending_soon');
                  setStatusFilter('all');
                  window.location.href = window.location.pathname;
                }}
              >
                Clear Filters
              </Button>
              <Button>
                Browse All Categories
              </Button>
            </div>
          </div>
        )}

        {/* Load More / Pagination */}
        {auctions.last_page > 1 && (
          <div className="mt-8">
            {auctions.current_page < auctions.last_page ? (
              <div className="text-center">
                <Button
                  variant="outline"
                  onClick={() => {
                    const params = new URLSearchParams(window.location.search);
                    params.set('page', (auctions.current_page + 1).toString());
                    window.location.href = `${window.location.pathname}?${params.toString()}`;
                  }}
                >
                  Load More Auctions
                </Button>
              </div>
            ) : (
              <div className="flex items-center justify-center space-x-2">
                {Array.from({ length: Math.min(auctions.last_page, 10) }, (_, i) => i + 1).map((page) => (
                  <Button
                    key={page}
                    variant={page === auctions.current_page ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => {
                      const params = new URLSearchParams(window.location.search);
                      params.set('page', page.toString());
                      window.location.href = `${window.location.pathname}?${params.toString()}`;
                    }}
                  >
                    {page}
                  </Button>
                ))}

                {auctions.last_page > 10 && (
                  <>
                    <span className="text-gray-500">...</span>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        const params = new URLSearchParams(window.location.search);
                        params.set('page', auctions.last_page.toString());
                        window.location.href = `${window.location.pathname}?${params.toString()}`;
                      }}
                    >
                      {auctions.last_page}
                    </Button>
                  </>
                )}
              </div>
            )}
          </div>
        )}
      </div>
    </AppLayout>
  );
};

export default CategoryAuctions;

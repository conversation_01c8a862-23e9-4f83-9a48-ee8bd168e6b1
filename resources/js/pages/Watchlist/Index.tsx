import React from 'react';
import { Head } from '@inertiajs/react';
import { PageProps } from '../../types';
import UserWatchlist from '../User/Watchlist';

// This is a simple redirect/alias to the User/Watchlist page
// since both routes might be used in the application

interface Props extends PageProps {
  auctions: {
    data: any[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
  };
}

const WatchlistIndex: React.FC<Props> = (props) => {
  return <UserWatchlist {...props} />;
};

export default WatchlistIndex;

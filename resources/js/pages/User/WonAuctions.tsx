import React, { useState } from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import {
  Trophy,
  Download,
  CreditCard,
  Package,
  Clock,
  CheckCircle,
  AlertTriangle,
  DollarSign,
  Calendar
} from 'lucide-react';
import { PageProps, Auction } from '../../types';
import AppLayout from '../../layouts/app-layout';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Badge } from '../../components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../components/ui/select';
import { Alert, AlertDescription } from '../../components/ui/alert';

interface Props extends PageProps {
  auctions: {
    data: Auction[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
  };
}

const UserWonAuctions: React.FC<Props> = ({ auctions }) => {
  const [sortBy, setSortBy] = useState('newest');
  const [statusFilter, setStatusFilter] = useState('all');

  const handleFilterChange = (key: string, value: string) => {
    const params = new URLSearchParams(window.location.search);
    if (value === 'all' || value === '') {
      params.delete(key);
    } else {
      params.set(key, value);
    }
    
    const newUrl = `${window.location.pathname}?${params.toString()}`;
    window.location.href = newUrl;
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount / 100);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  };

  const getPaymentStatus = (auction: Auction) => {
    // This would come from the auction data in a real implementation
    const paymentStatus = auction.metadata?.payment_status || 'pending';
    
    switch (paymentStatus) {
      case 'completed':
        return { icon: CheckCircle, color: 'text-green-600', bg: 'bg-green-100', label: 'Paid' };
      case 'pending':
        return { icon: Clock, color: 'text-yellow-600', bg: 'bg-yellow-100', label: 'Payment Due' };
      case 'failed':
        return { icon: AlertTriangle, color: 'text-red-600', bg: 'bg-red-100', label: 'Payment Failed' };
      default:
        return { icon: Clock, color: 'text-gray-600', bg: 'bg-gray-100', label: 'Unknown' };
    }
  };

  const getShippingStatus = (auction: Auction) => {
    // This would come from the auction data in a real implementation
    const shippingStatus = auction.metadata?.shipping_status || 'not_shipped';
    
    switch (shippingStatus) {
      case 'shipped':
        return { icon: Package, color: 'text-blue-600', bg: 'bg-blue-100', label: 'Shipped' };
      case 'delivered':
        return { icon: CheckCircle, color: 'text-green-600', bg: 'bg-green-100', label: 'Delivered' };
      case 'processing':
        return { icon: Clock, color: 'text-yellow-600', bg: 'bg-yellow-100', label: 'Processing' };
      default:
        return { icon: Package, color: 'text-gray-600', bg: 'bg-gray-100', label: 'Not Shipped' };
    }
  };

  // Calculate summary statistics
  const totalSpent = auctions.data.reduce((sum, auction) => 
    sum + (auction.final_price?.amount || auction.current_bid?.amount || 0), 0
  );
  const paidCount = auctions.data.filter(a => a.metadata?.payment_status === 'completed').length;
  const pendingPaymentCount = auctions.data.filter(a => a.metadata?.payment_status === 'pending').length;
  const shippedCount = auctions.data.filter(a => a.metadata?.shipping_status === 'shipped').length;

  return (
    <AppLayout>
      <Head title="Won Auctions" />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <Trophy className="h-8 w-8 text-yellow-500" />
            <h1 className="text-3xl font-bold text-gray-900">Won Auctions</h1>
          </div>
          <p className="text-gray-600">
            Congratulations! Here are the auctions you've won. Complete payment and track shipping.
          </p>
        </div>

        {/* Summary Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Won</p>
                  <p className="text-2xl font-bold text-gray-900">{auctions.total}</p>
                </div>
                <Trophy className="h-8 w-8 text-yellow-500" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Spent</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {formatCurrency(totalSpent, 'USD')}
                  </p>
                </div>
                <DollarSign className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Paid</p>
                  <p className="text-2xl font-bold text-gray-900">{paidCount}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Shipped</p>
                  <p className="text-2xl font-bold text-gray-900">{shippedCount}</p>
                </div>
                <Package className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Pending Payments Alert */}
        {pendingPaymentCount > 0 && (
          <Alert className="mb-6">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              You have {pendingPaymentCount} auction{pendingPaymentCount > 1 ? 's' : ''} with pending payments. 
              Please complete payment to avoid cancellation.
            </AlertDescription>
          </Alert>
        )}

        {/* Filters */}
        <div className="mb-6">
          <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
            <div className="flex items-center space-x-4">
              <Select value={sortBy} onValueChange={(value) => {
                setSortBy(value);
                handleFilterChange('sort', value);
              }}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="newest">Recently Won</SelectItem>
                  <SelectItem value="oldest">Oldest First</SelectItem>
                  <SelectItem value="price_high">Highest Price</SelectItem>
                  <SelectItem value="price_low">Lowest Price</SelectItem>
                  <SelectItem value="payment_due">Payment Due</SelectItem>
                </SelectContent>
              </Select>

              <Select value={statusFilter} onValueChange={(value) => {
                setStatusFilter(value);
                handleFilterChange('status', value);
              }}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Auctions</SelectItem>
                  <SelectItem value="payment_pending">Payment Pending</SelectItem>
                  <SelectItem value="paid">Paid</SelectItem>
                  <SelectItem value="shipped">Shipped</SelectItem>
                  <SelectItem value="delivered">Delivered</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </div>

        {/* Won Auctions List */}
        {auctions.data.length > 0 ? (
          <div className="space-y-6">
            {auctions.data.map((auction) => {
              const paymentStatus = getPaymentStatus(auction);
              const shippingStatus = getShippingStatus(auction);
              const PaymentIcon = paymentStatus.icon;
              const ShippingIcon = shippingStatus.icon;

              return (
                <Card key={auction.id} className="overflow-hidden">
                  <CardContent className="p-6">
                    <div className="flex items-start space-x-6">
                      {/* Auction Image */}
                      <div className="flex-shrink-0">
                        {auction.images && auction.images.length > 0 ? (
                          <img
                            src={auction.images[0].thumbnail_url || auction.images[0].url}
                            alt={auction.title}
                            className="w-24 h-24 rounded-lg object-cover"
                          />
                        ) : (
                          <div className="w-24 h-24 bg-gray-200 rounded-lg flex items-center justify-center">
                            <Package className="h-8 w-8 text-gray-400" />
                          </div>
                        )}
                      </div>

                      {/* Auction Details */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div>
                            <h3 className="text-lg font-semibold text-gray-900 mb-2">
                              <Link
                                href={`/auctions/${auction.id}`}
                                className="hover:text-blue-600"
                              >
                                {auction.title}
                              </Link>
                            </h3>
                            
                            <div className="flex items-center space-x-4 text-sm text-gray-600 mb-3">
                              <div className="flex items-center">
                                <Calendar className="h-4 w-4 mr-1" />
                                Won on {formatDate(auction.timing?.end_time || '')}
                              </div>
                              <div className="flex items-center">
                                <Trophy className="h-4 w-4 mr-1" />
                                Winning bid
                              </div>
                            </div>

                            <div className="flex items-center space-x-4">
                              <Badge className={`${paymentStatus.bg} ${paymentStatus.color}`}>
                                <PaymentIcon className="h-3 w-3 mr-1" />
                                {paymentStatus.label}
                              </Badge>
                              
                              <Badge className={`${shippingStatus.bg} ${shippingStatus.color}`}>
                                <ShippingIcon className="h-3 w-3 mr-1" />
                                {shippingStatus.label}
                              </Badge>
                            </div>
                          </div>

                          {/* Price and Actions */}
                          <div className="text-right">
                            <div className="text-2xl font-bold text-gray-900 mb-2">
                              {formatCurrency(
                                auction.final_price?.amount || auction.current_bid?.amount || 0,
                                auction.current_bid?.currency || 'USD'
                              )}
                            </div>
                            
                            <div className="space-y-2">
                              {auction.metadata?.payment_status === 'pending' && (
                                <Button size="sm" className="w-full">
                                  <CreditCard className="h-4 w-4 mr-2" />
                                  Pay Now
                                </Button>
                              )}
                              
                              {auction.metadata?.payment_status === 'completed' && (
                                <Button variant="outline" size="sm" className="w-full">
                                  <Download className="h-4 w-4 mr-2" />
                                  Receipt
                                </Button>
                              )}
                              
                              <Button variant="outline" size="sm" className="w-full">
                                View Details
                              </Button>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        ) : (
          <div className="text-center py-12">
            <Trophy className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No won auctions yet</h3>
            <p className="text-gray-600 mb-4">
              Start bidding on auctions to see your wins here. Good luck!
            </p>
            <Button asChild>
              <Link href="/auctions">
                Browse Auctions
              </Link>
            </Button>
          </div>
        )}

        {/* Pagination */}
        {auctions.last_page > 1 && (
          <div className="mt-8 flex items-center justify-center space-x-2">
            {Array.from({ length: auctions.last_page }, (_, i) => i + 1).map((page) => (
              <Button
                key={page}
                variant={page === auctions.current_page ? 'default' : 'outline'}
                size="sm"
                onClick={() => {
                  const params = new URLSearchParams(window.location.search);
                  params.set('page', page.toString());
                  window.location.href = `${window.location.pathname}?${params.toString()}`;
                }}
              >
                {page}
              </Button>
            ))}
          </div>
        )}
      </div>
    </AppLayout>
  );
};

export default UserWonAuctions;

import React, { useState } from 'react';
import { <PERSON>, <PERSON>, router } from '@inertiajs/react';
import { TrendingUp, Crown, Clock, X, Filter, Gavel } from 'lucide-react';
import { Bid, PaginatedData, PageProps } from '../../types';
import AppLayout from '../../layouts/app-layout';
import { Button } from '../../components/ui/button';
import { Card, CardContent } from '../../components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';
import { Badge } from '../../components/ui/badge';
import { cn } from '../../lib/utils';

interface Props extends PageProps {
  bids: PaginatedData<Bid>;
  filters: {
    status?: string;
    per_page: number;
  };
}

const UserBids: React.FC<Props> = ({ bids, filters }) => {
  const [selectedStatus, setSelectedStatus] = useState(filters.status || '');

  const statuses = [
    { id: '', name: 'All Bids' },
    { id: 'winning', name: 'Winning' },
    { id: 'outbid', name: 'Outbid' },
  ];

  const handleStatusChange = (status: string) => {
    setSelectedStatus(status);
    router.get('/my/bids', 
      { status: status || undefined },
      { preserveState: true, preserveScroll: true }
    );
  };

  const handleCancelBid = (bidId: number) => {
    if (confirm('Are you sure you want to cancel this bid? This action cannot be undone.')) {
      router.delete(`/bids/${bidId}`, {
        preserveScroll: true,
        onSuccess: () => {
          // Bid cancelled successfully
        },
      });
    }
  };

  const getBidStatusBadge = (bid: Bid) => {
    if (bid.status.is_winning) {
      return (
        <Badge className="bg-green-100 text-green-800">
          <Crown className="h-3 w-3 mr-1" />
          Winning
        </Badge>
      );
    } else if (bid.status.is_outbid) {
      return (
        <Badge variant="destructive">
          Outbid
        </Badge>
      );
    } else {
      return (
        <Badge variant="secondary">
          Active
        </Badge>
      );
    }
  };

  const formatTimeAgo = (timestamp: string): string => {
    const now = new Date().getTime();
    const bidTime = new Date(timestamp).getTime();
    const difference = now - bidTime;

    const minutes = Math.floor(difference / (1000 * 60));
    const hours = Math.floor(difference / (1000 * 60 * 60));
    const days = Math.floor(difference / (1000 * 60 * 60 * 24));

    if (days > 0) return `${days}d ago`;
    if (hours > 0) return `${hours}h ago`;
    if (minutes > 0) return `${minutes}m ago`;
    return 'Just now';
  };

  const canCancelBid = (bid: Bid) => {
    // Can only cancel if auction is still active and bid is the user's latest bid
    return bid.auction?.status === 'active' && bid.status.is_winning;
  };

  return (
    <AppLayout>
      <Head title="My Bids" />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-4">My Bids</h1>
          <p className="text-muted-foreground">
            Track all your bids and see which auctions you're winning
          </p>
        </div>

        {/* Filters */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Filter className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">
                  {bids.meta.total} bid{bids.meta.total !== 1 ? 's' : ''} found
                </span>
              </div>

              <div className="flex items-center space-x-4">
                <Select value={selectedStatus} onValueChange={handleStatusChange}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    {statuses.map((status) => (
                      <SelectItem key={status.id} value={status.id}>
                        {status.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Bids List */}
        {bids.data.length > 0 ? (
          <div className="space-y-4">
            {bids.data.map((bid) => (
              <Card key={bid.id} className="overflow-hidden">
                <CardContent className="p-0">
                  <div className="flex">
                    {/* Image */}
                    <div className="w-32 h-24 bg-muted flex-shrink-0">
                      {bid.auction?.images[0] ? (
                        <img
                          src={bid.auction.images[0].thumbnail_url || bid.auction.images[0].url}
                          alt={bid.auction.title}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <Gavel className="h-6 w-6 text-muted-foreground" />
                        </div>
                      )}
                    </div>

                    {/* Content */}
                    <div className="flex-1 p-4">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          {bid.auction && (
                            <Link href={`/auctions/${bid.auction.id}`}>
                              <h3 className="text-lg font-semibold text-foreground hover:text-primary mb-2">
                                {bid.auction.title}
                              </h3>
                            </Link>
                          )}

                          <div className="flex items-center space-x-4 mb-3">
                            {getBidStatusBadge(bid)}
                            <span className="text-sm text-muted-foreground">
                              {bid.bid_type === 'proxy' ? 'Proxy Bid' : 'Manual Bid'}
                            </span>
                            <span className="text-sm text-muted-foreground">
                              <Clock className="h-3 w-3 inline mr-1" />
                              {formatTimeAgo(bid.timing.timestamp)}
                            </span>
                          </div>

                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div>
                              <span className="text-muted-foreground">Your Bid:</span>
                              <div className="font-semibold text-primary">
                                {bid.amount.formatted}
                              </div>
                            </div>
                            {bid.max_bid && (
                              <div>
                                <span className="text-muted-foreground">Max Bid:</span>
                                <div className="font-semibold">
                                  {bid.max_bid.formatted}
                                </div>
                              </div>
                            )}
                            {bid.auction && (
                              <div>
                                <span className="text-muted-foreground">Current Bid:</span>
                                <div className="font-semibold">
                                  {bid.auction.current_bid.formatted}
                                </div>
                              </div>
                            )}
                            {bid.auction && (
                              <div>
                                <span className="text-muted-foreground">Status:</span>
                                <div className="font-semibold">
                                  {bid.auction.status.charAt(0).toUpperCase() + bid.auction.status.slice(1)}
                                </div>
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Actions */}
                        <div className="flex items-center space-x-2 ml-4">
                          {canCancelBid(bid) && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleCancelBid(bid.id)}
                              className="text-red-600 hover:text-red-700 hover:bg-red-50"
                            >
                              <X className="h-4 w-4 mr-1" />
                              Cancel
                            </Button>
                          )}

                          {bid.auction && (
                            <Link href={`/auctions/${bid.auction.id}`}>
                              <Button size="sm" variant="ghost">
                                View Auction
                              </Button>
                            </Link>
                          )}
                        </div>
                      </div>

                      {/* Additional Info for Winning Bids */}
                      {bid.status.is_winning && bid.auction?.status === 'active' && (
                        <div className="mt-3 p-3 bg-green-50 border border-green-200 rounded">
                          <div className="flex items-center text-green-800 text-sm">
                            <Crown className="h-4 w-4 mr-2" />
                            <span className="font-medium">
                              You're currently winning this auction!
                            </span>
                          </div>
                        </div>
                      )}

                      {/* Additional Info for Outbid */}
                      {bid.status.is_outbid && bid.auction?.status === 'active' && (
                        <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded">
                          <div className="flex items-center justify-between text-red-800 text-sm">
                            <span className="font-medium">
                              You've been outbid on this auction
                            </span>
                            {bid.auction && (
                              <Link href={`/auctions/${bid.auction.id}`}>
                                <Button size="sm" variant="outline" className="text-red-600 border-red-300">
                                  Place New Bid
                                </Button>
                              </Link>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <Card>
            <CardContent className="p-12 text-center">
              <TrendingUp className="h-16 w-16 mx-auto mb-4 text-muted-foreground opacity-50" />
              <h3 className="text-lg font-semibold mb-2">No bids found</h3>
              <p className="text-muted-foreground mb-4">
                {selectedStatus 
                  ? `You don't have any ${selectedStatus.toLowerCase()} bids.`
                  : "You haven't placed any bids yet."
                }
              </p>
              <Link href="/auctions">
                <Button>
                  <TrendingUp className="h-4 w-4 mr-2" />
                  Browse Auctions
                </Button>
              </Link>
            </CardContent>
          </Card>
        )}

        {/* Pagination */}
        {bids.meta.last_page > 1 && (
          <div className="mt-8 flex items-center justify-center space-x-2">
            {bids.links.prev && (
              <Button
                variant="outline"
                onClick={() => router.get(bids.links.prev!)}
              >
                Previous
              </Button>
            )}
            
            <span className="text-sm text-muted-foreground">
              Page {bids.meta.current_page} of {bids.meta.last_page}
            </span>
            
            {bids.links.next && (
              <Button
                variant="outline"
                onClick={() => router.get(bids.links.next!)}
              >
                Next
              </Button>
            )}
          </div>
        )}
      </div>
    </AppLayout>
  );
};

export default UserBids;

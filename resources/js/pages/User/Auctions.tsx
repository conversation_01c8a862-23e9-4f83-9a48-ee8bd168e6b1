import React, { useState } from 'react';
import { Head, Link, router } from '@inertiajs/react';
import { Gavel, Plus, Edit, Trash2, Play, Square, Filter } from 'lucide-react';
import { Auction, PaginatedData, PageProps } from '../../types';
import AppLayout from '../../layouts/app-layout';
import AuctionCard from '../../components/auction/AuctionCard';
import { Button } from '../../components/ui/button';
import { Card, CardContent } from '../../components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';
import { Badge } from '../../components/ui/badge';
import { cn } from '../../lib/utils';

interface Props extends PageProps {
  auctions: PaginatedData<Auction>;
  filters: {
    status?: string;
    per_page: number;
  };
}

const UserAuctions: React.FC<Props> = ({ auctions, filters }) => {
  const [selectedStatus, setSelectedStatus] = useState(filters.status || '');

  const statuses = [
    { id: '', name: 'All Statuses' },
    { id: 'draft', name: 'Draft' },
    { id: 'scheduled', name: 'Scheduled' },
    { id: 'active', name: 'Active' },
    { id: 'ended', name: 'Ended' },
  ];

  const handleStatusChange = (status: string) => {
    setSelectedStatus(status);
    router.get('/my/auctions', 
      { status: status || undefined },
      { preserveState: true, preserveScroll: true }
    );
  };

  const handleActivateAuction = (auctionId: number) => {
    router.post(`/auctions/${auctionId}/activate`, {}, {
      preserveScroll: true,
      onSuccess: () => {
        // Auction activated successfully
      },
    });
  };

  const handleEndAuction = (auctionId: number) => {
    if (confirm('Are you sure you want to end this auction? This action cannot be undone.')) {
      router.post(`/auctions/${auctionId}/end`, {}, {
        preserveScroll: true,
        onSuccess: () => {
          // Auction ended successfully
        },
      });
    }
  };

  const handleDeleteAuction = (auctionId: number) => {
    if (confirm('Are you sure you want to delete this auction? This action cannot be undone.')) {
      router.delete(`/auctions/${auctionId}`, {
        preserveScroll: true,
        onSuccess: () => {
          // Auction deleted successfully
        },
      });
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'bg-green-100 text-green-800';
      case 'ended':
        return 'bg-red-100 text-red-800';
      case 'scheduled':
        return 'bg-blue-100 text-blue-800';
      case 'draft':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const canActivate = (auction: Auction) => {
    return auction.status === 'draft' || auction.status === 'scheduled';
  };

  const canEnd = (auction: Auction) => {
    return auction.status === 'active';
  };

  const canEdit = (auction: Auction) => {
    return auction.metadata.can_edit;
  };

  const canDelete = (auction: Auction) => {
    return auction.status === 'draft' || (auction.status === 'ended' && auction.statistics.bids_count === 0);
  };

  return (
    <AppLayout>
      <Head title="My Auctions" />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="flex items-center justify-between mb-8">
          <div>
            <h1 className="text-3xl font-bold text-foreground mb-4">My Auctions</h1>
            <p className="text-muted-foreground">
              Manage your auction listings and track their performance
            </p>
          </div>
          <Link href="/auctions/create">
            <Button size="lg">
              <Plus className="h-4 w-4 mr-2" />
              Create Auction
            </Button>
          </Link>
        </div>

        {/* Filters */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Filter className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">
                  {auctions.meta.total} auction{auctions.meta.total !== 1 ? 's' : ''} found
                </span>
              </div>

              <div className="flex items-center space-x-4">
                <Select value={selectedStatus} onValueChange={handleStatusChange}>
                  <SelectTrigger className="w-48">
                    <SelectValue placeholder="Filter by status" />
                  </SelectTrigger>
                  <SelectContent>
                    {statuses.map((status) => (
                      <SelectItem key={status.id} value={status.id}>
                        {status.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Auctions List */}
        {auctions.data.length > 0 ? (
          <div className="space-y-6">
            {auctions.data.map((auction) => (
              <Card key={auction.id} className="overflow-hidden">
                <CardContent className="p-0">
                  <div className="flex">
                    {/* Image */}
                    <div className="w-48 h-32 bg-muted flex-shrink-0">
                      {auction.images[0] ? (
                        <img
                          src={auction.images[0].thumbnail_url || auction.images[0].url}
                          alt={auction.title}
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center">
                          <Gavel className="h-8 w-8 text-muted-foreground" />
                        </div>
                      )}
                    </div>

                    {/* Content */}
                    <div className="flex-1 p-6">
                      <div className="flex items-start justify-between">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <Link href={`/auctions/${auction.id}`}>
                              <h3 className="text-lg font-semibold text-foreground hover:text-primary">
                                {auction.title}
                              </h3>
                            </Link>
                            <Badge className={getStatusColor(auction.status)}>
                              {auction.status.charAt(0).toUpperCase() + auction.status.slice(1)}
                            </Badge>
                          </div>

                          <p className="text-muted-foreground text-sm mb-4 line-clamp-2">
                            {auction.description}
                          </p>

                          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                            <div>
                              <span className="text-muted-foreground">Current Bid:</span>
                              <div className="font-semibold text-primary">
                                {auction.current_bid.formatted}
                              </div>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Bids:</span>
                              <div className="font-semibold">
                                {auction.statistics.bids_count}
                              </div>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Views:</span>
                              <div className="font-semibold">
                                {auction.statistics.views_count}
                              </div>
                            </div>
                            <div>
                              <span className="text-muted-foreground">Watchers:</span>
                              <div className="font-semibold">
                                {auction.statistics.watchers_count}
                              </div>
                            </div>
                          </div>
                        </div>

                        {/* Actions */}
                        <div className="flex items-center space-x-2 ml-6">
                          {canActivate(auction) && (
                            <Button
                              size="sm"
                              onClick={() => handleActivateAuction(auction.id)}
                              className="bg-green-600 hover:bg-green-700"
                            >
                              <Play className="h-4 w-4 mr-1" />
                              Activate
                            </Button>
                          )}

                          {canEnd(auction) && (
                            <Button
                              size="sm"
                              variant="destructive"
                              onClick={() => handleEndAuction(auction.id)}
                            >
                              <Square className="h-4 w-4 mr-1" />
                              End
                            </Button>
                          )}

                          {canEdit(auction) && (
                            <Link href={`/auctions/${auction.id}/edit`}>
                              <Button size="sm" variant="outline">
                                <Edit className="h-4 w-4 mr-1" />
                                Edit
                              </Button>
                            </Link>
                          )}

                          {canDelete(auction) && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => handleDeleteAuction(auction.id)}
                              className="text-red-600 hover:text-red-700 hover:bg-red-50"
                            >
                              <Trash2 className="h-4 w-4 mr-1" />
                              Delete
                            </Button>
                          )}

                          <Link href={`/auctions/${auction.id}`}>
                            <Button size="sm" variant="ghost">
                              View
                            </Button>
                          </Link>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <Card>
            <CardContent className="p-12 text-center">
              <Gavel className="h-16 w-16 mx-auto mb-4 text-muted-foreground opacity-50" />
              <h3 className="text-lg font-semibold mb-2">No auctions found</h3>
              <p className="text-muted-foreground mb-4">
                {selectedStatus 
                  ? `You don't have any ${selectedStatus} auctions.`
                  : "You haven't created any auctions yet."
                }
              </p>
              <Link href="/auctions/create">
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Your First Auction
                </Button>
              </Link>
            </CardContent>
          </Card>
        )}

        {/* Pagination */}
        {auctions.meta.last_page > 1 && (
          <div className="mt-8 flex items-center justify-center space-x-2">
            {auctions.links.prev && (
              <Button
                variant="outline"
                onClick={() => router.get(auctions.links.prev!)}
              >
                Previous
              </Button>
            )}
            
            <span className="text-sm text-muted-foreground">
              Page {auctions.meta.current_page} of {auctions.meta.last_page}
            </span>
            
            {auctions.links.next && (
              <Button
                variant="outline"
                onClick={() => router.get(auctions.links.next!)}
              >
                Next
              </Button>
            )}
          </div>
        )}
      </div>
    </AppLayout>
  );
};

export default UserAuctions;

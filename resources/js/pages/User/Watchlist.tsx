import React, { useState } from 'react';
import { Head, router } from '@inertiajs/react';
import {
  Heart,
  Grid,
  List,
  Filter,
  Clock,
  Gavel,
  Eye,
  X,
  AlertCircle
} from 'lucide-react';
import { PageProps, Auction } from '../../types';
import AppLayout from '../../layouts/app-layout';
import AuctionCard from '../../components/auction/AuctionCard';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Badge } from '../../components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../components/ui/select';
import { Alert, AlertDescription } from '../../components/ui/alert';
import { cn } from '../../lib/utils';

interface Props extends PageProps {
  auctions: {
    data: Auction[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
  };
}

const UserWatchlist: React.FC<Props> = ({ auctions }) => {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState('ending_soon');
  const [statusFilter, setStatusFilter] = useState('all');
  const [removingIds, setRemovingIds] = useState<Set<number>>(new Set());

  const handleRemoveFromWatchlist = async (auctionId: number) => {
    setRemovingIds(prev => new Set(prev).add(auctionId));
    
    try {
      await router.delete(`/watchlist/${auctionId}`, {
        preserveScroll: true,
        onSuccess: () => {
          // Remove from local state
          setRemovingIds(prev => {
            const newSet = new Set(prev);
            newSet.delete(auctionId);
            return newSet;
          });
        },
        onError: () => {
          setRemovingIds(prev => {
            const newSet = new Set(prev);
            newSet.delete(auctionId);
            return newSet;
          });
        }
      });
    } catch (error) {
      console.error('Failed to remove from watchlist:', error);
      setRemovingIds(prev => {
        const newSet = new Set(prev);
        newSet.delete(auctionId);
        return newSet;
      });
    }
  };

  const handleFilterChange = (key: string, value: string) => {
    const params = new URLSearchParams(window.location.search);
    if (value === 'all' || value === '') {
      params.delete(key);
    } else {
      params.set(key, value);
    }
    
    const newUrl = `${window.location.pathname}?${params.toString()}`;
    router.get(newUrl, {}, { preserveState: true });
  };

  const filteredAuctions = auctions.data.filter(auction => {
    if (statusFilter === 'all') return true;
    if (statusFilter === 'ending_soon') return auction.timing?.is_ending_soon;
    return auction.status === statusFilter;
  });

  const sortedAuctions = [...filteredAuctions].sort((a, b) => {
    switch (sortBy) {
      case 'ending_soon':
        return (a.timing?.remaining_minutes || 0) - (b.timing?.remaining_minutes || 0);
      case 'newest':
        return new Date(b.metadata?.created_at || '').getTime() - new Date(a.metadata?.created_at || '').getTime();
      case 'price_low':
        return (a.current_bid?.amount || 0) - (b.current_bid?.amount || 0);
      case 'price_high':
        return (b.current_bid?.amount || 0) - (a.current_bid?.amount || 0);
      case 'most_bids':
        return (b.statistics?.bids_count || 0) - (a.statistics?.bids_count || 0);
      default:
        return 0;
    }
  });

  const getStatusCounts = () => {
    return {
      all: auctions.data.length,
      active: auctions.data.filter(a => a.status === 'active').length,
      ending_soon: auctions.data.filter(a => a.timing?.is_ending_soon).length,
      ended: auctions.data.filter(a => a.status === 'ended').length,
    };
  };

  const statusCounts = getStatusCounts();

  return (
    <AppLayout>
      <Head title="My Watchlist" />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <Heart className="h-8 w-8 text-red-500" />
            <h1 className="text-3xl font-bold text-gray-900">My Watchlist</h1>
          </div>
          <p className="text-gray-600">
            Keep track of auctions you're interested in. Get notified when they're ending soon.
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-gray-900">{statusCounts.all}</div>
              <div className="text-sm text-gray-600">Total Watched</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-green-600">{statusCounts.active}</div>
              <div className="text-sm text-gray-600">Active</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-yellow-600">{statusCounts.ending_soon}</div>
              <div className="text-sm text-gray-600">Ending Soon</div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4 text-center">
              <div className="text-2xl font-bold text-gray-600">{statusCounts.ended}</div>
              <div className="text-sm text-gray-600">Ended</div>
            </CardContent>
          </Card>
        </div>

        {/* Ending Soon Alert */}
        {statusCounts.ending_soon > 0 && (
          <Alert className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              You have {statusCounts.ending_soon} auction{statusCounts.ending_soon > 1 ? 's' : ''} ending soon! 
              Don't miss your chance to bid.
            </AlertDescription>
          </Alert>
        )}

        {/* Filters and Controls */}
        <div className="mb-6">
          <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
            <div className="flex items-center space-x-4">
              <Select value={sortBy} onValueChange={(value) => {
                setSortBy(value);
                handleFilterChange('sort', value);
              }}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="ending_soon">Ending Soon</SelectItem>
                  <SelectItem value="newest">Recently Added</SelectItem>
                  <SelectItem value="price_low">Price: Low to High</SelectItem>
                  <SelectItem value="price_high">Price: High to Low</SelectItem>
                  <SelectItem value="most_bids">Most Bids</SelectItem>
                </SelectContent>
              </Select>

              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All ({statusCounts.all})</SelectItem>
                  <SelectItem value="active">Active ({statusCounts.active})</SelectItem>
                  <SelectItem value="ending_soon">Ending Soon ({statusCounts.ending_soon})</SelectItem>
                  <SelectItem value="ended">Ended ({statusCounts.ended})</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="flex items-center space-x-2">
              <Button
                variant={viewMode === 'grid' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('grid')}
              >
                <Grid className="w-4 h-4" />
              </Button>
              <Button
                variant={viewMode === 'list' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setViewMode('list')}
              >
                <List className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Watchlist Content */}
        {sortedAuctions.length > 0 ? (
          <div className={cn(
            viewMode === 'grid'
              ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
              : 'space-y-4'
          )}>
            {sortedAuctions.map((auction) => (
              <div key={auction.id} className="relative group">
                <AuctionCard
                  auction={auction}
                  variant={viewMode === 'list' ? 'horizontal' : 'vertical'}
                  onWatchToggle={(auctionId, isWatched) => {
                    if (!isWatched) {
                      handleRemoveFromWatchlist(auctionId);
                    }
                  }}
                />
                
                {/* Remove Button */}
                <Button
                  variant="destructive"
                  size="sm"
                  className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                  onClick={() => handleRemoveFromWatchlist(auction.id)}
                  disabled={removingIds.has(auction.id)}
                >
                  <X className="h-4 w-4" />
                </Button>
                
                {/* Status Badges */}
                <div className="absolute top-2 left-2 space-y-1">
                  {auction.timing?.is_ending_soon && (
                    <Badge className="bg-yellow-100 text-yellow-800">
                      <Clock className="h-3 w-3 mr-1" />
                      Ending Soon
                    </Badge>
                  )}
                  
                  {auction.metadata?.user_has_bid && (
                    <Badge className="bg-blue-100 text-blue-800">
                      <Gavel className="h-3 w-3 mr-1" />
                      You Bid
                    </Badge>
                  )}
                </div>
              </div>
            ))}
          </div>
        ) : auctions.data.length === 0 ? (
          <div className="text-center py-12">
            <Heart className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">Your watchlist is empty</h3>
            <p className="text-gray-600 mb-4">
              Start watching auctions to keep track of items you're interested in.
            </p>
            <Button asChild>
              <a href="/auctions">
                <Eye className="h-4 w-4 mr-2" />
                Browse Auctions
              </a>
            </Button>
          </div>
        ) : (
          <div className="text-center py-12">
            <Filter className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No auctions match your filters</h3>
            <p className="text-gray-600 mb-4">
              Try adjusting your filters to see more auctions.
            </p>
            <Button
              variant="outline"
              onClick={() => {
                setStatusFilter('all');
                setSortBy('ending_soon');
              }}
            >
              Clear Filters
            </Button>
          </div>
        )}

        {/* Pagination */}
        {auctions.last_page > 1 && (
          <div className="mt-8 flex items-center justify-center space-x-2">
            {Array.from({ length: auctions.last_page }, (_, i) => i + 1).map((page) => (
              <Button
                key={page}
                variant={page === auctions.current_page ? 'default' : 'outline'}
                size="sm"
                onClick={() => {
                  const params = new URLSearchParams(window.location.search);
                  params.set('page', page.toString());
                  router.get(`${window.location.pathname}?${params.toString()}`);
                }}
              >
                {page}
              </Button>
            ))}
          </div>
        )}
      </div>
    </AppLayout>
  );
};

export default UserWatchlist;

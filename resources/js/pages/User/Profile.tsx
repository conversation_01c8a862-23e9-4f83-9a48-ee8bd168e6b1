import React, { useState } from 'react';
import { Head, useForm } from '@inertiajs/react';
import {
  User,
  Mail,
  Phone,
  MapPin,
  Calendar,
  Shield,
  Edit,
  Save,
  X,
  Camera,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { PageProps } from '../../types';
import AppLayout from '../../layouts/app-layout';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Label } from '../../components/ui/label';
import { Textarea } from '../../components/ui/textarea';
import { Badge } from '../../components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '../../components/ui/avatar';
import { Alert, AlertDescription } from '../../components/ui/alert';

interface UserProfile {
  id: number;
  name: string;
  email: string;
  phone?: string;
  bio?: string;
  location?: string;
  role: string;
  avatar?: {
    url?: string;
    thumbnail?: string;
  };
  verification: {
    is_email_verified: boolean;
    is_phone_verified: boolean;
    is_identity_verified: boolean;
    verification_level: string;
  };
  status: {
    is_active: boolean;
    last_login_at?: string;
  };
  statistics: {
    auctions_created: number;
    auctions_won: number;
    total_spent: number;
    total_earned: number;
    feedback_score: number;
    feedback_count: number;
  };
  created_at: string;
}

interface Props extends PageProps {
  user: UserProfile;
}

const UserProfile: React.FC<Props> = ({ user }) => {
  const [isEditing, setIsEditing] = useState(false);
  const [selectedAvatar, setSelectedAvatar] = useState<File | null>(null);

  const { data, setData, put, processing, errors, reset } = useForm({
    name: user.name || '',
    email: user.email || '',
    phone: user.phone || '',
    bio: user.bio || '',
    location: user.location || '',
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    put('/profile', {
      onSuccess: () => {
        setIsEditing(false);
        setSelectedAvatar(null);
      },
    });
  };

  const handleCancel = () => {
    reset();
    setIsEditing(false);
    setSelectedAvatar(null);
  };

  const handleAvatarChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setSelectedAvatar(e.target.files[0]);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
    }).format(amount / 100);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });
  };

  const getRoleBadge = (role: string) => {
    const roleConfig = {
      admin: { color: 'bg-red-100 text-red-800', label: 'Admin' },
      moderator: { color: 'bg-purple-100 text-purple-800', label: 'Moderator' },
      premium_seller: { color: 'bg-blue-100 text-blue-800', label: 'Premium Seller' },
      seller: { color: 'bg-green-100 text-green-800', label: 'Seller' },
      user: { color: 'bg-gray-100 text-gray-800', label: 'User' },
    };

    const config = roleConfig[role as keyof typeof roleConfig] || roleConfig.user;
    return <Badge className={config.color}>{config.label}</Badge>;
  };

  const getVerificationLevel = () => {
    const { verification } = user;
    if (verification.is_identity_verified) {
      return { icon: CheckCircle, color: 'text-green-600', label: 'Fully Verified' };
    }
    if (verification.is_email_verified && verification.is_phone_verified) {
      return { icon: CheckCircle, color: 'text-blue-600', label: 'Email & Phone Verified' };
    }
    if (verification.is_email_verified) {
      return { icon: AlertCircle, color: 'text-yellow-600', label: 'Email Verified' };
    }
    return { icon: AlertCircle, color: 'text-red-600', label: 'Unverified' };
  };

  const verificationStatus = getVerificationLevel();
  const VerificationIcon = verificationStatus.icon;

  return (
    <AppLayout>
      <Head title="My Profile" />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">My Profile</h1>
          <p className="text-gray-600 mt-2">
            Manage your account information and preferences.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Profile Card */}
          <div className="lg:col-span-1">
            <Card>
              <CardContent className="p-6 text-center">
                <div className="relative inline-block mb-4">
                  <Avatar className="w-24 h-24">
                    <AvatarImage 
                      src={selectedAvatar ? URL.createObjectURL(selectedAvatar) : user.avatar?.url} 
                    />
                    <AvatarFallback className="text-2xl">
                      {user.name.charAt(0).toUpperCase()}
                    </AvatarFallback>
                  </Avatar>
                  
                  {isEditing && (
                    <label className="absolute bottom-0 right-0 bg-blue-600 text-white p-2 rounded-full cursor-pointer hover:bg-blue-700">
                      <Camera className="h-4 w-4" />
                      <input
                        type="file"
                        accept="image/*"
                        onChange={handleAvatarChange}
                        className="hidden"
                      />
                    </label>
                  )}
                </div>

                <h2 className="text-xl font-semibold text-gray-900 mb-2">{user.name}</h2>
                <p className="text-gray-600 mb-4">{user.email}</p>
                
                <div className="space-y-2 mb-4">
                  {getRoleBadge(user.role)}
                  
                  <div className="flex items-center justify-center space-x-2">
                    <VerificationIcon className={`h-4 w-4 ${verificationStatus.color}`} />
                    <span className={`text-sm ${verificationStatus.color}`}>
                      {verificationStatus.label}
                    </span>
                  </div>
                </div>

                <div className="text-sm text-gray-600">
                  <div className="flex items-center justify-center space-x-1 mb-1">
                    <Calendar className="h-4 w-4" />
                    <span>Joined {formatDate(user.created_at)}</span>
                  </div>
                  
                  {user.status.last_login_at && (
                    <div>
                      Last active: {formatDate(user.status.last_login_at)}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Statistics Card */}
            <Card className="mt-6">
              <CardHeader>
                <CardTitle className="text-lg">Statistics</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex justify-between">
                  <span className="text-gray-600">Auctions Created</span>
                  <span className="font-semibold">{user.statistics.auctions_created}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-600">Auctions Won</span>
                  <span className="font-semibold">{user.statistics.auctions_won}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Spent</span>
                  <span className="font-semibold">{formatCurrency(user.statistics.total_spent)}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-600">Total Earned</span>
                  <span className="font-semibold">{formatCurrency(user.statistics.total_earned)}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-600">Feedback Score</span>
                  <span className="font-semibold">
                    {user.statistics.feedback_score}% ({user.statistics.feedback_count})
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Profile Form */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Profile Information</CardTitle>
                  {!isEditing ? (
                    <Button onClick={() => setIsEditing(true)}>
                      <Edit className="h-4 w-4 mr-2" />
                      Edit Profile
                    </Button>
                  ) : (
                    <div className="space-x-2">
                      <Button variant="outline" onClick={handleCancel}>
                        <X className="h-4 w-4 mr-2" />
                        Cancel
                      </Button>
                      <Button onClick={handleSubmit} disabled={processing}>
                        <Save className="h-4 w-4 mr-2" />
                        {processing ? 'Saving...' : 'Save'}
                      </Button>
                    </div>
                  )}
                </div>
              </CardHeader>
              
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <Label htmlFor="name">Full Name</Label>
                      <Input
                        id="name"
                        value={data.name}
                        onChange={(e) => setData('name', e.target.value)}
                        disabled={!isEditing}
                      />
                      {errors.name && (
                        <p className="text-sm text-red-600 mt-1">{errors.name}</p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="email">Email Address</Label>
                      <Input
                        id="email"
                        type="email"
                        value={data.email}
                        onChange={(e) => setData('email', e.target.value)}
                        disabled={!isEditing}
                      />
                      {errors.email && (
                        <p className="text-sm text-red-600 mt-1">{errors.email}</p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="phone">Phone Number</Label>
                      <Input
                        id="phone"
                        value={data.phone}
                        onChange={(e) => setData('phone', e.target.value)}
                        disabled={!isEditing}
                        placeholder="Enter your phone number"
                      />
                      {errors.phone && (
                        <p className="text-sm text-red-600 mt-1">{errors.phone}</p>
                      )}
                    </div>

                    <div>
                      <Label htmlFor="location">Location</Label>
                      <Input
                        id="location"
                        value={data.location}
                        onChange={(e) => setData('location', e.target.value)}
                        disabled={!isEditing}
                        placeholder="City, Country"
                      />
                      {errors.location && (
                        <p className="text-sm text-red-600 mt-1">{errors.location}</p>
                      )}
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="bio">Bio</Label>
                    <Textarea
                      id="bio"
                      value={data.bio}
                      onChange={(e) => setData('bio', e.target.value)}
                      disabled={!isEditing}
                      placeholder="Tell us about yourself..."
                      rows={4}
                    />
                    {errors.bio && (
                      <p className="text-sm text-red-600 mt-1">{errors.bio}</p>
                    )}
                  </div>
                </form>
              </CardContent>
            </Card>

            {/* Verification Card */}
            <Card className="mt-6">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Shield className="h-5 w-5 mr-2" />
                  Account Verification
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Mail className="h-4 w-4 text-gray-500" />
                    <span>Email Verification</span>
                  </div>
                  {user.verification.is_email_verified ? (
                    <Badge className="bg-green-100 text-green-800">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Verified
                    </Badge>
                  ) : (
                    <Button variant="outline" size="sm">
                      Verify Email
                    </Button>
                  )}
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Phone className="h-4 w-4 text-gray-500" />
                    <span>Phone Verification</span>
                  </div>
                  {user.verification.is_phone_verified ? (
                    <Badge className="bg-green-100 text-green-800">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Verified
                    </Badge>
                  ) : (
                    <Button variant="outline" size="sm">
                      Verify Phone
                    </Button>
                  )}
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Shield className="h-4 w-4 text-gray-500" />
                    <span>Identity Verification</span>
                  </div>
                  {user.verification.is_identity_verified ? (
                    <Badge className="bg-green-100 text-green-800">
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Verified
                    </Badge>
                  ) : (
                    <Button variant="outline" size="sm">
                      Verify Identity
                    </Button>
                  )}
                </div>

                {!user.verification.is_identity_verified && (
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      Complete identity verification to increase your account limits and build trust with other users.
                    </AlertDescription>
                  </Alert>
                )}
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AppLayout>
  );
};

export default UserProfile;

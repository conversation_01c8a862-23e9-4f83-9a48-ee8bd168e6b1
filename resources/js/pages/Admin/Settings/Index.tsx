import React, { useState } from 'react';
import { Head, useForm } from '@inertiajs/react';
import { Settings, Save, RotateCcw, Shield, Globe, Clock, DollarSign } from 'lucide-react';
import { PageProps } from '../../../types';
import AppLayout from '../../../layouts/app-layout';
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card';
import { But<PERSON> } from '../../../components/ui/button';
import { Switch } from '../../../components/ui/switch';
import { Input } from '../../../components/ui/input';
import { Label } from '../../../components/ui/label';
import { Textarea } from '../../../components/ui/textarea';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '../../../components/ui/tabs';
import { Badge } from '../../../components/ui/badge';

interface Setting {
  id: number;
  key: string;
  value: string;
  type: 'string' | 'boolean' | 'integer' | 'float' | 'json';
  description: string;
  is_public: boolean;
}

interface SettingsPageProps extends PageProps {
  settings: Record<string, Setting[]>;
  categories: Record<string, string>;
}

const SettingsIndex: React.FC<SettingsPageProps> = ({ settings, categories }) => {
  const [activeTab, setActiveTab] = useState('allow');
  const { data, setData, put, processing, errors, reset } = useForm<{
    settings: Array<{
      key: string;
      value: any;
      type: string;
    }>;
  }>({
    settings: [],
  });

  const handleSettingChange = (key: string, value: any, type: string) => {
    const existingIndex = data.settings.findIndex(s => s.key === key);
    const newSetting = { key, value, type };
    
    if (existingIndex >= 0) {
      const newSettings = [...data.settings];
      newSettings[existingIndex] = newSetting;
      setData('settings', newSettings);
    } else {
      setData('settings', [...data.settings, newSetting]);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    put('/admin/settings');
  };

  const renderSettingInput = (setting: Setting) => {
    const currentValue = data.settings.find(s => s.key === setting.key)?.value ?? 
                        (setting.type === 'boolean' ? setting.value === '1' : setting.value);

    switch (setting.type) {
      case 'boolean':
        return (
          <div className="flex items-center space-x-2">
            <Switch
              id={setting.key}
              checked={currentValue}
              onCheckedChange={(checked) => handleSettingChange(setting.key, checked, setting.type)}
            />
            <Label htmlFor={setting.key} className="text-sm font-medium">
              {currentValue ? 'Enabled' : 'Disabled'}
            </Label>
          </div>
        );
      
      case 'integer':
      case 'float':
        return (
          <Input
            type="number"
            value={currentValue}
            onChange={(e) => handleSettingChange(setting.key, e.target.value, setting.type)}
            step={setting.type === 'float' ? '0.01' : '1'}
          />
        );
      
      case 'json':
        return (
          <Textarea
            value={typeof currentValue === 'string' ? currentValue : JSON.stringify(currentValue, null, 2)}
            onChange={(e) => handleSettingChange(setting.key, e.target.value, setting.type)}
            rows={4}
          />
        );
      
      default:
        return (
          <Input
            type="text"
            value={currentValue}
            onChange={(e) => handleSettingChange(setting.key, e.target.value, setting.type)}
          />
        );
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'allow':
        return <Shield className="h-4 w-4" />;
      case 'site':
        return <Globe className="h-4 w-4" />;
      case 'max':
      case 'min':
        return <Clock className="h-4 w-4" />;
      case 'default':
        return <DollarSign className="h-4 w-4" />;
      default:
        return <Settings className="h-4 w-4" />;
    }
  };

  return (
    <AppLayout>
      <Head title="Admin Settings" />
      
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white flex items-center gap-2">
            <Settings className="h-8 w-8" />
            System Settings
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Configure global application settings and permissions
          </p>
        </div>

        <form onSubmit={handleSubmit}>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid w-full grid-cols-4 lg:grid-cols-8">
              {Object.entries(categories).map(([key, label]) => (
                <TabsTrigger key={key} value={key} className="flex items-center gap-1">
                  {getCategoryIcon(key)}
                  <span className="hidden sm:inline">{label}</span>
                </TabsTrigger>
              ))}
            </TabsList>

            {Object.entries(settings).map(([category, categorySettings]) => (
              <TabsContent key={category} value={category}>
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      {getCategoryIcon(category)}
                      {categories[category] || category}
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    {categorySettings.map((setting) => (
                      <div key={setting.key} className="space-y-2">
                        <div className="flex items-center justify-between">
                          <div className="space-y-1">
                            <Label htmlFor={setting.key} className="text-sm font-medium">
                              {setting.key.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                            </Label>
                            <p className="text-xs text-gray-500 dark:text-gray-400">
                              {setting.description}
                            </p>
                          </div>
                          <div className="flex items-center gap-2">
                            {setting.is_public && (
                              <Badge variant="secondary" className="text-xs">
                                Public
                              </Badge>
                            )}
                            <Badge variant="outline" className="text-xs">
                              {setting.type}
                            </Badge>
                          </div>
                        </div>
                        {renderSettingInput(setting)}
                      </div>
                    ))}
                  </CardContent>
                </Card>
              </TabsContent>
            ))}
          </Tabs>

          <div className="flex justify-end gap-4 mt-8">
            <Button
              type="button"
              variant="outline"
              onClick={() => reset()}
              disabled={processing}
            >
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </Button>
            <Button type="submit" disabled={processing}>
              <Save className="h-4 w-4 mr-2" />
              {processing ? 'Saving...' : 'Save Settings'}
            </Button>
          </div>
        </form>
      </div>
    </AppLayout>
  );
};

export default SettingsIndex;

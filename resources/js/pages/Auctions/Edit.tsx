import React, { useState } from 'react';
import { Head, useForm } from '@inertiajs/react';
import { Save, X, Upload, Calendar, DollarSign, Package } from 'lucide-react';
import { PageProps, Auction, Category } from '../../types';
import AppLayout from '../../layouts/app-layout';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Label } from '../../components/ui/label';
import { Textarea } from '../../components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../components/ui/select';
import { Alert, AlertDescription } from '../../components/ui/alert';

interface Props extends PageProps {
  auction: Auction;
  categories: Category[];
}

const AuctionEdit: React.FC<Props> = ({ auction, categories }) => {
  const { data, setData, put, processing, errors } = useForm({
    title: auction.title || '',
    description: auction.description || '',
    category_id: auction.category?.id || '',
    starting_price: auction.starting_price?.amount ? (auction.starting_price.amount / 100).toString() : '',
    reserve_price: auction.reserve_price?.amount ? (auction.reserve_price.amount / 100).toString() : '',
    bid_increment: auction.bid_increment?.amount ? (auction.bid_increment.amount / 100).toString() : '',
    condition: auction.condition || 'good',
    start_time: auction.timing?.start_time ? new Date(auction.timing.start_time).toISOString().slice(0, 16) : '',
    end_time: auction.timing?.end_time ? new Date(auction.timing.end_time).toISOString().slice(0, 16) : '',
    auto_extend: auction.features?.auto_extend || false,
    extend_minutes: auction.features?.extend_minutes || 10,
  });

  const [selectedImages, setSelectedImages] = useState<File[]>([]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    put(`/auctions/${auction.id}`);
  };

  const handleImageSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files) {
      setSelectedImages(Array.from(e.target.files));
    }
  };

  const conditionOptions = [
    { value: 'new', label: 'New' },
    { value: 'like_new', label: 'Like New' },
    { value: 'excellent', label: 'Excellent' },
    { value: 'good', label: 'Good' },
    { value: 'fair', label: 'Fair' },
    { value: 'poor', label: 'Poor' },
  ];

  return (
    <AppLayout>
      <Head title={`Edit Auction: ${auction.title}`} />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Edit Auction</h1>
          <p className="text-gray-600 mt-2">
            Update your auction details. Changes will be saved immediately.
          </p>
        </div>

        {auction.status !== 'scheduled' && (
          <Alert className="mb-6">
            <AlertDescription>
              This auction has already started. Only limited changes are allowed.
            </AlertDescription>
          </Alert>
        )}

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Package className="h-5 w-5 mr-2" />
                Basic Information
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <Label htmlFor="title">Title *</Label>
                <Input
                  id="title"
                  value={data.title}
                  onChange={(e) => setData('title', e.target.value)}
                  placeholder="Enter auction title"
                  disabled={auction.status !== 'scheduled'}
                />
                {errors.title && (
                  <p className="text-sm text-red-600 mt-1">{errors.title}</p>
                )}
              </div>

              <div>
                <Label htmlFor="description">Description *</Label>
                <Textarea
                  id="description"
                  value={data.description}
                  onChange={(e) => setData('description', e.target.value)}
                  placeholder="Describe your item in detail"
                  rows={4}
                  disabled={auction.status !== 'scheduled'}
                />
                {errors.description && (
                  <p className="text-sm text-red-600 mt-1">{errors.description}</p>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="category">Category *</Label>
                  <Select
                    value={data.category_id}
                    onValueChange={(value) => setData('category_id', value)}
                    disabled={auction.status !== 'scheduled'}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.id.toString()}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.category_id && (
                    <p className="text-sm text-red-600 mt-1">{errors.category_id}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="condition">Condition *</Label>
                  <Select
                    value={data.condition}
                    onValueChange={(value) => setData('condition', value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select condition" />
                    </SelectTrigger>
                    <SelectContent>
                      {conditionOptions.map((option) => (
                        <SelectItem key={option.value} value={option.value}>
                          {option.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.condition && (
                    <p className="text-sm text-red-600 mt-1">{errors.condition}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Pricing */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <DollarSign className="h-5 w-5 mr-2" />
                Pricing
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <Label htmlFor="starting_price">Starting Price *</Label>
                  <Input
                    id="starting_price"
                    type="number"
                    step="0.01"
                    value={data.starting_price}
                    onChange={(e) => setData('starting_price', e.target.value)}
                    placeholder="0.00"
                    disabled={auction.status !== 'scheduled'}
                  />
                  {errors.starting_price && (
                    <p className="text-sm text-red-600 mt-1">{errors.starting_price}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="reserve_price">Reserve Price</Label>
                  <Input
                    id="reserve_price"
                    type="number"
                    step="0.01"
                    value={data.reserve_price}
                    onChange={(e) => setData('reserve_price', e.target.value)}
                    placeholder="0.00"
                    disabled={auction.status !== 'scheduled'}
                  />
                  {errors.reserve_price && (
                    <p className="text-sm text-red-600 mt-1">{errors.reserve_price}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="bid_increment">Bid Increment *</Label>
                  <Input
                    id="bid_increment"
                    type="number"
                    step="0.01"
                    value={data.bid_increment}
                    onChange={(e) => setData('bid_increment', e.target.value)}
                    placeholder="1.00"
                    disabled={auction.status !== 'scheduled'}
                  />
                  {errors.bid_increment && (
                    <p className="text-sm text-red-600 mt-1">{errors.bid_increment}</p>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Timing */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calendar className="h-5 w-5 mr-2" />
                Timing
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="start_time">Start Time *</Label>
                  <Input
                    id="start_time"
                    type="datetime-local"
                    value={data.start_time}
                    onChange={(e) => setData('start_time', e.target.value)}
                    disabled={auction.status !== 'scheduled'}
                  />
                  {errors.start_time && (
                    <p className="text-sm text-red-600 mt-1">{errors.start_time}</p>
                  )}
                </div>

                <div>
                  <Label htmlFor="end_time">End Time *</Label>
                  <Input
                    id="end_time"
                    type="datetime-local"
                    value={data.end_time}
                    onChange={(e) => setData('end_time', e.target.value)}
                  />
                  {errors.end_time && (
                    <p className="text-sm text-red-600 mt-1">{errors.end_time}</p>
                  )}
                </div>
              </div>

              <div className="flex items-center space-x-4">
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={data.auto_extend}
                    onChange={(e) => setData('auto_extend', e.target.checked)}
                    className="rounded border-gray-300"
                  />
                  <span className="text-sm">Auto-extend auction</span>
                </label>

                {data.auto_extend && (
                  <div className="flex items-center space-x-2">
                    <Label htmlFor="extend_minutes" className="text-sm">by</Label>
                    <Input
                      id="extend_minutes"
                      type="number"
                      value={data.extend_minutes}
                      onChange={(e) => setData('extend_minutes', parseInt(e.target.value))}
                      className="w-20"
                      min="1"
                      max="60"
                    />
                    <span className="text-sm">minutes</span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Actions */}
          <div className="flex items-center justify-between">
            <Button
              type="button"
              variant="outline"
              onClick={() => window.history.back()}
            >
              <X className="h-4 w-4 mr-2" />
              Cancel
            </Button>

            <Button type="submit" disabled={processing}>
              <Save className="h-4 w-4 mr-2" />
              {processing ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
        </form>
      </div>
    </AppLayout>
  );
};

export default AuctionEdit;

import React, { useState } from 'react';
import { Head, router } from '@inertiajs/react';
import { Clock, Grid, List, Gavel, AlertTriangle } from 'lucide-react';
import { Auction, PaginatedData, PageProps } from '../../types';
import AppLayout from '../../layouts/app-layout';
import AuctionCard from '../../components/auction/AuctionCard';
import { Button } from '../../components/ui/button';
import { Card, CardContent } from '../../components/ui/card';
import { Badge } from '../../components/ui/badge';
import { cn } from '../../lib/utils';

interface Props extends PageProps {
  auctions: PaginatedData<Auction>;
}

const EndingSoonAuctions: React.FC<Props> = ({ auctions }) => {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  const handleWatchToggle = (auctionId: number, isWatched: boolean) => {
    // The AuctionCard component handles the actual API call
    // This is just for UI feedback if needed
  };

  return (
    <AppLayout>
      <Head title="Ending Soon - Auctions" />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-orange-100 rounded-lg">
              <Clock className="h-6 w-6 text-orange-600" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900">Ending Soon</h1>
            <Badge variant="secondary" className="bg-orange-100 text-orange-800">
              Urgent
            </Badge>
          </div>
          <p className="text-gray-600">
            Don't miss out! These auctions are ending within the next 24 hours
          </p>
        </div>

        {/* Alert Banner */}
        <Card className="mb-8 border-orange-200 bg-orange-50">
          <CardContent className="p-4">
            <div className="flex items-center space-x-3">
              <AlertTriangle className="h-5 w-5 text-orange-600" />
              <div>
                <p className="text-sm font-medium text-orange-800">
                  Time is running out!
                </p>
                <p className="text-sm text-orange-700">
                  These auctions will end soon. Place your bids now to avoid missing out.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Stats and View Controls */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Clock className="h-4 w-4 text-orange-500" />
                <span className="text-sm text-gray-600">
                  {auctions.meta.total} auction{auctions.meta.total !== 1 ? 's' : ''} ending soon
                </span>
              </div>

              <div className="flex items-center space-x-2">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                >
                  <Grid className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Ending Soon Auctions Grid/List */}
        {auctions.data.length > 0 ? (
          <div className={cn(
            viewMode === 'grid' 
              ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
              : 'space-y-4'
          )}>
            {auctions.data.map((auction) => (
              <div key={auction.id} className="relative">
                {/* Ending Soon Badge */}
                <div className="absolute top-2 left-2 z-10">
                  <Badge className="bg-orange-500 text-white">
                    <Clock className="h-3 w-3 mr-1" />
                    Ending Soon
                  </Badge>
                </div>
                <AuctionCard
                  auction={auction}
                  onWatchToggle={handleWatchToggle}
                  className={cn(
                    "border-orange-200 shadow-lg",
                    viewMode === 'list' ? 'flex-row' : ''
                  )}
                />
              </div>
            ))}
          </div>
        ) : (
          <Card>
            <CardContent className="p-12 text-center">
              <Clock className="h-16 w-16 mx-auto mb-4 text-gray-400 opacity-50" />
              <h3 className="text-lg font-semibold mb-2">No auctions ending soon</h3>
              <p className="text-gray-600 mb-4">
                All current auctions have plenty of time remaining. Check back later or browse all active auctions.
              </p>
              <Button 
                onClick={() => router.get('/auctions')}
                variant="outline"
              >
                Browse All Auctions
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Pagination */}
        {auctions.meta.last_page > 1 && (
          <div className="mt-8 flex items-center justify-center space-x-2">
            {auctions.links.prev && (
              <Button
                variant="outline"
                onClick={() => router.get(auctions.links.prev!)}
              >
                Previous
              </Button>
            )}
            
            <span className="text-sm text-gray-600">
              Page {auctions.meta.current_page} of {auctions.meta.last_page}
            </span>
            
            {auctions.links.next && (
              <Button
                variant="outline"
                onClick={() => router.get(auctions.links.next!)}
              >
                Next
              </Button>
            )}
          </div>
        )}

        {/* Call to Action */}
        <Card className="mt-12 bg-gradient-to-r from-orange-50 to-red-50 border-orange-200">
          <CardContent className="p-8 text-center">
            <AlertTriangle className="h-12 w-12 mx-auto mb-4 text-orange-600" />
            <h3 className="text-xl font-semibold mb-2 text-gray-900">Don't wait too long!</h3>
            <p className="text-gray-600 mb-4">
              Set up bid alerts to get notified when auctions you're interested in are about to end.
            </p>
            <Button className="bg-orange-600 hover:bg-orange-700 text-white">
              Set Up Bid Alerts
            </Button>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
};

export default EndingSoonAuctions;

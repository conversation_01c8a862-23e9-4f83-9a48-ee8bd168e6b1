import React, { useState } from 'react';
import { Head, router } from '@inertiajs/react';
import { Star, Grid, List, Gavel, Crown } from 'lucide-react';
import { Auction, PaginatedData, PageProps } from '../../types';
import AppLayout from '../../layouts/app-layout';
import AuctionCard from '../../components/auction/AuctionCard';
import { Button } from '../../components/ui/button';
import { Card, CardContent } from '../../components/ui/card';
import { Badge } from '../../components/ui/badge';
import { cn } from '../../lib/utils';

interface Props extends PageProps {
  auctions: PaginatedData<Auction>;
}

const FeaturedAuctions: React.FC<Props> = ({ auctions }) => {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  const handleWatchToggle = (auctionId: number, isWatched: boolean) => {
    // The AuctionCard component handles the actual API call
    // This is just for UI feedback if needed
  };

  return (
    <AppLayout>
      <Head title="Featured Auctions" />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <div className="p-2 bg-yellow-100 rounded-lg">
              <Crown className="h-6 w-6 text-yellow-600" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900">Featured Auctions</h1>
            <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">
              Premium
            </Badge>
          </div>
          <p className="text-gray-600">
            Discover our handpicked selection of premium auctions with exceptional items
          </p>
        </div>

        {/* Stats and View Controls */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Star className="h-4 w-4 text-yellow-500" />
                <span className="text-sm text-gray-600">
                  {auctions.meta.total} featured auction{auctions.meta.total !== 1 ? 's' : ''} available
                </span>
              </div>

              <div className="flex items-center space-x-2">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                >
                  <Grid className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Featured Auctions Grid/List */}
        {auctions.data.length > 0 ? (
          <div className={cn(
            viewMode === 'grid' 
              ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
              : 'space-y-4'
          )}>
            {auctions.data.map((auction) => (
              <div key={auction.id} className="relative">
                {/* Featured Badge */}
                <div className="absolute top-2 left-2 z-10">
                  <Badge className="bg-yellow-500 text-white">
                    <Star className="h-3 w-3 mr-1" />
                    Featured
                  </Badge>
                </div>
                <AuctionCard
                  auction={auction}
                  onWatchToggle={handleWatchToggle}
                  className={cn(
                    "border-yellow-200 shadow-lg",
                    viewMode === 'list' ? 'flex-row' : ''
                  )}
                />
              </div>
            ))}
          </div>
        ) : (
          <Card>
            <CardContent className="p-12 text-center">
              <Crown className="h-16 w-16 mx-auto mb-4 text-gray-400 opacity-50" />
              <h3 className="text-lg font-semibold mb-2">No featured auctions available</h3>
              <p className="text-gray-600 mb-4">
                Check back soon for new featured auctions, or browse all active auctions.
              </p>
              <Button 
                onClick={() => router.get('/auctions')}
                variant="outline"
              >
                Browse All Auctions
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Pagination */}
        {auctions.meta.last_page > 1 && (
          <div className="mt-8 flex items-center justify-center space-x-2">
            {auctions.links.prev && (
              <Button
                variant="outline"
                onClick={() => router.get(auctions.links.prev!)}
              >
                Previous
              </Button>
            )}
            
            <span className="text-sm text-gray-600">
              Page {auctions.meta.current_page} of {auctions.meta.last_page}
            </span>
            
            {auctions.links.next && (
              <Button
                variant="outline"
                onClick={() => router.get(auctions.links.next!)}
              >
                Next
              </Button>
            )}
          </div>
        )}

        {/* Call to Action */}
        <Card className="mt-12 bg-gradient-to-r from-yellow-50 to-orange-50 border-yellow-200">
          <CardContent className="p-8 text-center">
            <Crown className="h-12 w-12 mx-auto mb-4 text-yellow-600" />
            <h3 className="text-xl font-semibold mb-2 text-gray-900">Want your auction featured?</h3>
            <p className="text-gray-600 mb-4">
              Increase your auction's visibility and attract more bidders with our featured listing service.
            </p>
            <Button className="bg-yellow-600 hover:bg-yellow-700 text-white">
              Learn More About Featured Listings
            </Button>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
};

export default FeaturedAuctions;

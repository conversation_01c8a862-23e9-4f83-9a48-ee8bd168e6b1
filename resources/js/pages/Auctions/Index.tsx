import React, { useState } from 'react';
import { Head, router } from '@inertiajs/react';
import { Search, Filter, Grid, List, Gavel } from 'lucide-react';
import { Auction, Category, PaginatedData, AuctionFilters, PageProps } from '../../types';
import AppLayout from '../../layouts/app-layout';
import AuctionCard from '../../components/auction/AuctionCard';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Card, CardContent } from '../../components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';
import { cn } from '../../lib/utils';

interface Props extends PageProps {
  auctions: PaginatedData<Auction>;
  categories: Category[];
  filters: AuctionFilters;
}

const AuctionsIndex: React.FC<Props> = ({ auctions, categories, filters }) => {
  const [searchQuery, setSearchQuery] = useState(filters.search || '');
  const [selectedCategory, setSelectedCategory] = useState(filters.category?.toString() || '');
  const [selectedStatus, setSelectedStatus] = useState(filters.status || 'active');
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');

  const statuses = [
    { id: '', name: 'All Statuses' },
    { id: 'active', name: 'Active' },
    { id: 'scheduled', name: 'Scheduled' },
    { id: 'ended', name: 'Ended' },
  ];

  const handleSearch = () => {
    const searchFilters: AuctionFilters = {
      search: searchQuery || undefined,
      category: selectedCategory ? parseInt(selectedCategory) : undefined,
      status: selectedStatus || undefined,
    };

    router.get('/auctions', searchFilters, {
      preserveState: true,
      preserveScroll: true,
    });
  };

  const handleWatchToggle = (auctionId: number, isWatched: boolean) => {
    // The AuctionCard component handles the actual API call
    // This is just for UI feedback if needed
  };

  return (
    <AppLayout>
      <Head title="Browse Auctions" />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-4">Browse Auctions</h1>
          <p className="text-muted-foreground">
            Discover amazing items from sellers around the world
          </p>
        </div>

        {/* Filters */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
              <div className="md:col-span-2">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="text"
                    placeholder="Search auctions..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                    onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                  />
                </div>
              </div>

              <div>
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger>
                    <SelectValue placeholder="All Categories" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="">All Categories</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id.toString()}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div>
                <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                  <SelectTrigger>
                    <SelectValue placeholder="All Statuses" />
                  </SelectTrigger>
                  <SelectContent>
                    {statuses.map((status) => (
                      <SelectItem key={status.id} value={status.id}>
                        {status.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                <Filter className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm text-muted-foreground">
                  {auctions.meta.total} auction{auctions.meta.total !== 1 ? 's' : ''} found
                </span>
              </div>

              <div className="flex items-center space-x-2">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                >
                  <Grid className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                >
                  <List className="h-4 w-4" />
                </Button>
                <Button onClick={handleSearch} size="sm">
                  Apply Filters
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Auctions Grid/List */}
        {auctions.data.length > 0 ? (
          <div className={cn(
            viewMode === 'grid' 
              ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
              : 'space-y-4'
          )}>
            {auctions.data.map((auction) => (
              <AuctionCard
                key={auction.id}
                auction={auction}
                onWatchToggle={handleWatchToggle}
                className={viewMode === 'list' ? 'flex-row' : ''}
              />
            ))}
          </div>
        ) : (
          <Card>
            <CardContent className="p-12 text-center">
              <Gavel className="h-16 w-16 mx-auto mb-4 text-muted-foreground opacity-50" />
              <h3 className="text-lg font-semibold mb-2">No auctions found</h3>
              <p className="text-muted-foreground mb-4">
                Try adjusting your search criteria or browse all auctions.
              </p>
              <Button 
                onClick={() => {
                  setSearchQuery('');
                  setSelectedCategory('');
                  setSelectedStatus('');
                  handleSearch();
                }}
                variant="outline"
              >
                Clear Filters
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Pagination */}
        {auctions.meta.last_page > 1 && (
          <div className="mt-8 flex items-center justify-center space-x-2">
            {auctions.links.prev && (
              <Button
                variant="outline"
                onClick={() => router.get(auctions.links.prev!)}
              >
                Previous
              </Button>
            )}
            
            <span className="text-sm text-muted-foreground">
              Page {auctions.meta.current_page} of {auctions.meta.last_page}
            </span>
            
            {auctions.links.next && (
              <Button
                variant="outline"
                onClick={() => router.get(auctions.links.next!)}
              >
                Next
              </Button>
            )}
          </div>
        )}
      </div>
    </AppLayout>
  );
};

export default AuctionsIndex;

import React, { useState } from 'react';
import { Head, useForm } from '@inertiajs/react';
import { Upload, DollarSign, Calendar, Clock, Package, Info } from 'lucide-react';
import { Category, CreateAuctionForm, PageProps } from '../../types';
import AppLayout from '../../layouts/app-layout';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Label } from '../../components/ui/label';
import { Textarea } from '../../components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../components/ui/select';
import { Checkbox } from '../../components/ui/checkbox';
import { Alert, AlertDescription } from '../../components/ui/alert';
import { cn } from '../../lib/utils';

interface Props extends PageProps {
  categories: Category[];
}

const CreateAuction: React.FC<Props> = ({ categories }) => {
  const [selectedImages, setSelectedImages] = useState<File[]>([]);
  const [imagePreviews, setImagePreviews] = useState<string[]>([]);

  const { data, setData, post, processing, errors, reset } = useForm<CreateAuctionForm>({
    category_id: 0,
    title: '',
    description: '',
    condition: '',
    starting_price: 0,
    reserve_price: undefined,
    buyout_price: undefined,
    currency: 'USD',
    start_time: '',
    end_time: '',
    shipping_cost: undefined,
    auto_extend: false,
    extend_minutes: 10,
  });

  const conditions = [
    { value: 'new', label: 'New' },
    { value: 'like_new', label: 'Like New' },
    { value: 'good', label: 'Good' },
    { value: 'fair', label: 'Fair' },
    { value: 'poor', label: 'Poor' },
  ];

  const currencies = [
    { value: 'USD', label: 'USD ($)' },
    { value: 'EUR', label: 'EUR (€)' },
    { value: 'GBP', label: 'GBP (£)' },
    { value: 'CAD', label: 'CAD (C$)' },
  ];

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(e.target.files || []);
    if (files.length + selectedImages.length > 10) {
      alert('Maximum 10 images allowed');
      return;
    }

    setSelectedImages(prev => [...prev, ...files]);

    // Create previews
    files.forEach(file => {
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreviews(prev => [...prev, e.target?.result as string]);
      };
      reader.readAsDataURL(file);
    });
  };

  const removeImage = (index: number) => {
    setSelectedImages(prev => prev.filter((_, i) => i !== index));
    setImagePreviews(prev => prev.filter((_, i) => i !== index));
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();

    const formData = new FormData();
    
    // Add form data
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        formData.append(key, value.toString());
      }
    });

    // Add images
    selectedImages.forEach((file, index) => {
      formData.append(`images[${index}]`, file);
    });

    post('/auctions', {
      data: formData,
      forceFormData: true,
    });
  };

  const getMinEndTime = () => {
    const startTime = data.start_time ? new Date(data.start_time) : new Date();
    const minEndTime = new Date(startTime.getTime() + 30 * 60 * 1000); // 30 minutes minimum
    return minEndTime.toISOString().slice(0, 16);
  };

  return (
    <AppLayout>
      <Head title="Create Auction" />
      
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-foreground mb-4">Create New Auction</h1>
          <p className="text-muted-foreground">
            List your item for auction and reach buyers worldwide
          </p>
        </div>

        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Basic Information */}
          <Card>
            <CardHeader>
              <CardTitle>Basic Information</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="title">Title *</Label>
                  <Input
                    id="title"
                    value={data.title}
                    onChange={(e) => setData('title', e.target.value)}
                    placeholder="Enter auction title"
                    required
                  />
                  {errors.title && <p className="text-sm text-red-600">{errors.title}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="category">Category *</Label>
                  <Select value={data.category_id.toString()} onValueChange={(value) => setData('category_id', parseInt(value))}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select category" />
                    </SelectTrigger>
                    <SelectContent>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.id.toString()}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  {errors.category_id && <p className="text-sm text-red-600">{errors.category_id}</p>}
                </div>
              </div>

              <div className="space-y-2">
                <Label htmlFor="description">Description *</Label>
                <Textarea
                  id="description"
                  value={data.description}
                  onChange={(e) => setData('description', e.target.value)}
                  placeholder="Describe your item in detail..."
                  rows={6}
                  required
                />
                {errors.description && <p className="text-sm text-red-600">{errors.description}</p>}
              </div>

              <div className="space-y-2">
                <Label htmlFor="condition">Condition</Label>
                <Select value={data.condition} onValueChange={(value) => setData('condition', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select condition" />
                  </SelectTrigger>
                  <SelectContent>
                    {conditions.map((condition) => (
                      <SelectItem key={condition.value} value={condition.value}>
                        {condition.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </CardContent>
          </Card>

          {/* Images */}
          <Card>
            <CardHeader>
              <CardTitle>Images</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6 text-center">
                  <Upload className="h-12 w-12 mx-auto mb-4 text-muted-foreground" />
                  <div className="space-y-2">
                    <Label htmlFor="images" className="cursor-pointer text-primary hover:text-primary/80">
                      Click to upload images
                    </Label>
                    <Input
                      id="images"
                      type="file"
                      multiple
                      accept="image/*"
                      onChange={handleImageUpload}
                      className="hidden"
                    />
                    <p className="text-sm text-muted-foreground">
                      Upload up to 10 images. First image will be the main image.
                    </p>
                  </div>
                </div>

                {imagePreviews.length > 0 && (
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {imagePreviews.map((preview, index) => (
                      <div key={index} className="relative group">
                        <img
                          src={preview}
                          alt={`Preview ${index + 1}`}
                          className="w-full h-32 object-cover rounded border"
                        />
                        <Button
                          type="button"
                          variant="destructive"
                          size="sm"
                          className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
                          onClick={() => removeImage(index)}
                        >
                          ×
                        </Button>
                        {index === 0 && (
                          <div className="absolute bottom-2 left-2 bg-primary text-primary-foreground text-xs px-2 py-1 rounded">
                            Main
                          </div>
                        )}
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Pricing */}
          <Card>
            <CardHeader>
              <CardTitle>Pricing & Duration</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="starting_price">Starting Price *</Label>
                  <div className="relative">
                    <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="starting_price"
                      type="number"
                      step="0.01"
                      min="0.01"
                      value={data.starting_price || ''}
                      onChange={(e) => setData('starting_price', parseFloat(e.target.value) || 0)}
                      className="pl-10"
                      required
                    />
                  </div>
                  {errors.starting_price && <p className="text-sm text-red-600">{errors.starting_price}</p>}
                </div>

                <div className="space-y-2">
                  <Label htmlFor="reserve_price">Reserve Price</Label>
                  <div className="relative">
                    <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="reserve_price"
                      type="number"
                      step="0.01"
                      min="0.01"
                      value={data.reserve_price || ''}
                      onChange={(e) => setData('reserve_price', parseFloat(e.target.value) || undefined)}
                      className="pl-10"
                      placeholder="Optional"
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="buyout_price">Buy Now Price</Label>
                  <div className="relative">
                    <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="buyout_price"
                      type="number"
                      step="0.01"
                      min="0.01"
                      value={data.buyout_price || ''}
                      onChange={(e) => setData('buyout_price', parseFloat(e.target.value) || undefined)}
                      className="pl-10"
                      placeholder="Optional"
                    />
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="start_time">Start Time</Label>
                  <Input
                    id="start_time"
                    type="datetime-local"
                    value={data.start_time}
                    onChange={(e) => setData('start_time', e.target.value)}
                    min={new Date().toISOString().slice(0, 16)}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="end_time">End Time *</Label>
                  <Input
                    id="end_time"
                    type="datetime-local"
                    value={data.end_time}
                    onChange={(e) => setData('end_time', e.target.value)}
                    min={getMinEndTime()}
                    required
                  />
                  {errors.end_time && <p className="text-sm text-red-600">{errors.end_time}</p>}
                </div>
              </div>

              <div className="space-y-4">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="auto_extend"
                    checked={data.auto_extend}
                    onCheckedChange={(checked) => setData('auto_extend', checked as boolean)}
                  />
                  <Label htmlFor="auto_extend">Enable auto-extend</Label>
                </div>

                {data.auto_extend && (
                  <div className="pl-6 space-y-2">
                    <Label htmlFor="extend_minutes">Extend by (minutes)</Label>
                    <Input
                      id="extend_minutes"
                      type="number"
                      min="1"
                      max="60"
                      value={data.extend_minutes}
                      onChange={(e) => setData('extend_minutes', parseInt(e.target.value) || 10)}
                      className="w-32"
                    />
                  </div>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Submit */}
          <div className="flex items-center justify-between">
            <Button type="button" variant="outline" onClick={() => window.history.back()}>
              Cancel
            </Button>
            <Button type="submit" disabled={processing} size="lg">
              {processing ? 'Creating...' : 'Create Auction'}
            </Button>
          </div>

          {errors.error && (
            <Alert variant="destructive">
              <AlertDescription>{errors.error}</AlertDescription>
            </Alert>
          )}
        </form>
      </div>
    </AppLayout>
  );
};

export default CreateAuction;

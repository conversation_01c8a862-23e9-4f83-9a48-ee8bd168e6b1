import React, { useState } from 'react';
import { <PERSON>, <PERSON> } from '@inertiajs/react';
import {
  TrendingUp,
  Clock,
  User,
  DollarSign,
  Filter,
  ChevronLeft,
  Trophy,
  AlertCircle
} from 'lucide-react';
import { PageProps, Auction, Bid } from '../../types';
import AppLayout from '../../layouts/app-layout';
import { Card, CardContent, CardHeader, CardTitle } from '../../components/ui/card';
import { Button } from '../../components/ui/button';
import { Badge } from '../../components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '../../components/ui/avatar';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../../components/ui/select';
import { cn } from '../../lib/utils';

interface Props extends PageProps {
  auction: Auction;
  bids: {
    data: Bid[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
  };
}

const BidsIndex: React.FC<Props> = ({ auction, bids }) => {
  const [sortBy, setSortBy] = useState('newest');
  const [filterBy, setFilterBy] = useState('all');

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency,
    }).format(amount / 100);
  };

  const formatTimeAgo = (timestamp: string): string => {
    const now = new Date();
    const time = new Date(timestamp);
    const diffInSeconds = Math.floor((now.getTime() - time.getTime()) / 1000);

    if (diffInSeconds < 60) return 'Just now';
    if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;
    if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;
    if (diffInSeconds < 604800) return `${Math.floor(diffInSeconds / 86400)}d ago`;
    
    return time.toLocaleDateString();
  };

  const getBidTypeColor = (type: string) => {
    switch (type) {
      case 'proxy':
        return 'bg-blue-100 text-blue-800';
      case 'auto':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getBidStatusIcon = (bid: Bid) => {
    if (bid.is_winning) {
      return <Trophy className="h-4 w-4 text-yellow-500" />;
    }
    if (!bid.is_valid) {
      return <AlertCircle className="h-4 w-4 text-red-500" />;
    }
    return null;
  };

  return (
    <AppLayout>
      <Head title={`Bids for ${auction.title}`} />
      
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-4 mb-4">
            <Button variant="outline" size="sm" asChild>
              <Link href={`/auctions/${auction.id}`}>
                <ChevronLeft className="h-4 w-4 mr-2" />
                Back to Auction
              </Link>
            </Button>
          </div>
          
          <h1 className="text-3xl font-bold text-gray-900 mb-2">Bid History</h1>
          <p className="text-gray-600">
            All bids for: <Link href={`/auctions/${auction.id}`} className="text-blue-600 hover:underline">
              {auction.title}
            </Link>
          </p>
        </div>

        {/* Auction Summary */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">
                  {formatCurrency(auction.current_bid?.amount || 0, auction.current_bid?.currency || 'USD')}
                </div>
                <div className="text-sm text-gray-600">Current Bid</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">
                  {bids.total}
                </div>
                <div className="text-sm text-gray-600">Total Bids</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">
                  {auction.statistics?.watchers_count || 0}
                </div>
                <div className="text-sm text-gray-600">Watchers</div>
              </div>
              
              <div className="text-center">
                <div className="text-2xl font-bold text-gray-900">
                  {auction.timing?.remaining_minutes ? Math.floor(auction.timing.remaining_minutes / 60) : 0}h
                </div>
                <div className="text-sm text-gray-600">Time Left</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Filters */}
        <div className="mb-6">
          <div className="flex flex-col sm:flex-row gap-4 items-center justify-between">
            <div className="flex items-center space-x-4">
              <Select value={sortBy} onValueChange={setSortBy}>
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="newest">Newest First</SelectItem>
                  <SelectItem value="oldest">Oldest First</SelectItem>
                  <SelectItem value="highest">Highest Amount</SelectItem>
                  <SelectItem value="lowest">Lowest Amount</SelectItem>
                </SelectContent>
              </Select>

              <Select value={filterBy} onValueChange={setFilterBy}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Filter" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Bids</SelectItem>
                  <SelectItem value="winning">Winning</SelectItem>
                  <SelectItem value="proxy">Proxy Bids</SelectItem>
                  <SelectItem value="regular">Regular Bids</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="text-sm text-gray-600">
              Showing {bids.data.length} of {bids.total} bids
            </div>
          </div>
        </div>

        {/* Bids List */}
        {bids.data.length > 0 ? (
          <div className="space-y-4">
            {bids.data.map((bid, index) => (
              <Card key={bid.id} className={cn(
                'transition-all duration-200',
                bid.is_winning && 'ring-2 ring-yellow-400 bg-yellow-50',
                !bid.is_valid && 'opacity-60'
              )}>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="flex items-center space-x-2">
                        <span className="text-lg font-bold text-gray-600">
                          #{bids.total - ((bids.current_page - 1) * bids.per_page) - index}
                        </span>
                        {getBidStatusIcon(bid)}
                      </div>
                      
                      <Avatar className="h-10 w-10">
                        <AvatarImage src={bid.bidder?.avatar?.url} />
                        <AvatarFallback>
                          {bid.bidder?.name?.charAt(0).toUpperCase() || 'A'}
                        </AvatarFallback>
                      </Avatar>
                      
                      <div>
                        <div className="font-medium text-gray-900">
                          {bid.bidder?.name || 'Anonymous Bidder'}
                        </div>
                        <div className="text-sm text-gray-500">
                          {formatTimeAgo(bid.timestamp)}
                        </div>
                      </div>
                    </div>
                    
                    <div className="text-right">
                      <div className="text-2xl font-bold text-gray-900">
                        {formatCurrency(bid.amount, bid.currency || 'USD')}
                      </div>
                      
                      <div className="flex items-center space-x-2 mt-1">
                        <Badge className={getBidTypeColor(bid.bid_type)}>
                          {bid.bid_type === 'proxy' ? 'Proxy' : 'Regular'}
                        </Badge>
                        
                        {bid.is_winning && (
                          <Badge className="bg-yellow-100 text-yellow-800">
                            Winning
                          </Badge>
                        )}
                        
                        {!bid.is_valid && (
                          <Badge variant="destructive">
                            Invalid
                          </Badge>
                        )}
                      </div>
                      
                      {bid.bid_type === 'proxy' && bid.max_bid && (
                        <div className="text-sm text-gray-500 mt-1">
                          Max: {formatCurrency(bid.max_bid, bid.currency || 'USD')}
                        </div>
                      )}
                    </div>
                  </div>
                  
                  {!bid.is_valid && bid.invalidation_reason && (
                    <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
                      <div className="flex items-center space-x-2">
                        <AlertCircle className="h-4 w-4 text-red-600" />
                        <span className="text-sm text-red-800">
                          Invalid: {bid.invalidation_reason}
                        </span>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <TrendingUp className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No bids yet</h3>
            <p className="text-gray-600 mb-4">
              Be the first to place a bid on this auction!
            </p>
            <Button asChild>
              <Link href={`/auctions/${auction.id}`}>
                <DollarSign className="h-4 w-4 mr-2" />
                Place First Bid
              </Link>
            </Button>
          </div>
        )}

        {/* Pagination */}
        {bids.last_page > 1 && (
          <div className="mt-8 flex items-center justify-center space-x-2">
            {Array.from({ length: bids.last_page }, (_, i) => i + 1).map((page) => (
              <Button
                key={page}
                variant={page === bids.current_page ? 'default' : 'outline'}
                size="sm"
                onClick={() => {
                  const params = new URLSearchParams(window.location.search);
                  params.set('page', page.toString());
                  window.location.href = `${window.location.pathname}?${params.toString()}`;
                }}
              >
                {page}
              </Button>
            ))}
          </div>
        )}
      </div>
    </AppLayout>
  );
};

export default BidsIndex;

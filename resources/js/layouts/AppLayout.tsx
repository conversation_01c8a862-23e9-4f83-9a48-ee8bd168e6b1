import React, { useState } from 'react';
import { Link, usePage } from '@inertiajs/react';
import { Search, Bell, User, Gavel, Plus, LogOut, Menu, X } from 'lucide-react';
import { PageProps } from '@/types';

interface AppLayoutProps {
  children: React.ReactNode;
}

const AppLayout: React.FC<AppLayoutProps> = ({ children }) => {
  const { auth, settings } = usePage<PageProps>().props;
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  // Check if user can create auctions
  const canCreateAuctions = auth.user && (
    auth.user.role === 'admin' ||
    settings.allow_user_auction_creation
  );

  const navigation = [
    { name: 'Home', href: '/' },
    { name: 'Auctions', href: '/auctions' },
    { name: 'Categories', href: '/categories' },
  ];

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b border-gray-200">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            {/* Logo */}
            <div className="flex items-center">
              <Link href="/" className="flex items-center space-x-2">
                <Gavel className="h-8 w-8 text-blue-600" />
                <span className="text-xl font-bold text-gray-900">AuctionHub</span>
              </Link>
            </div>

            {/* Desktop Navigation */}
            <nav className="hidden md:flex space-x-8">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="text-gray-600 hover:text-gray-900 px-3 py-2 text-sm font-medium transition-colors"
                >
                  {item.name}
                </Link>
              ))}
            </nav>

            {/* Search Bar */}
            <div className="hidden md:flex flex-1 max-w-lg mx-8">
              <div className="relative w-full">
                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <Search className="h-5 w-5 text-gray-400" />
                </div>
                <input
                  type="text"
                  placeholder="Search auctions..."
                  className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                />
              </div>
            </div>

            {/* Right side */}
            <div className="flex items-center space-x-4">
              {auth.user ? (
                <>
                  {/* Create Auction Button - Only show if user can create auctions */}
                  {canCreateAuctions && (
                    <Link href="/auctions/create">
                      <button className="hidden md:flex btn-primary btn-sm">
                        <Plus className="h-4 w-4 mr-2" />
                        Create Auction
                      </button>
                    </Link>
                  )}

                  {/* Notifications */}
                  <button className="p-2 text-gray-600 hover:text-gray-900 relative">
                    <Bell className="h-6 w-6" />
                    <span className="absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white"></span>
                  </button>

                  {/* User Menu */}
                  <div className="relative">
                    <Link href="/dashboard" className="flex items-center space-x-2 text-gray-600 hover:text-gray-900">
                      <div className="h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center">
                        <User className="h-5 w-5 text-blue-600" />
                      </div>
                      <span className="hidden md:block text-sm font-medium">{auth.user.name}</span>
                    </Link>
                  </div>

                  {/* Logout */}
                  <Link
                    href="/logout"
                    method="post"
                    as="button"
                    className="p-2 text-gray-600 hover:text-gray-900"
                  >
                    <LogOut className="h-5 w-5" />
                  </Link>
                </>
              ) : (
                <div className="flex items-center space-x-4">
                  <Link href="/login">
                    <button className="btn-ghost btn-sm">
                      Sign In
                    </button>
                  </Link>
                  <Link href="/register">
                    <button className="btn-primary btn-sm">
                      Sign Up
                    </button>
                  </Link>
                </div>
              )}

              {/* Mobile menu button */}
              <button
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className="md:hidden p-2 text-gray-600 hover:text-gray-900"
              >
                {isMobileMenuOpen ? (
                  <X className="h-6 w-6" />
                ) : (
                  <Menu className="h-6 w-6" />
                )}
              </button>
            </div>
          </div>
        </div>

        {/* Mobile menu */}
        {isMobileMenuOpen && (
          <div className="md:hidden">
            <div className="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white border-t border-gray-200">
              {navigation.map((item) => (
                <Link
                  key={item.name}
                  href={item.href}
                  className="text-gray-600 hover:text-gray-900 block px-3 py-2 text-base font-medium"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  {item.name}
                </Link>
              ))}

              {/* Mobile Search */}
              <div className="px-3 py-2">
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <Search className="h-5 w-5 text-gray-400" />
                  </div>
                  <input
                    type="text"
                    placeholder="Search auctions..."
                    className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  />
                </div>
              </div>

              {auth.user && canCreateAuctions && (
                <Link
                  href="/auctions/create"
                  className="text-gray-600 hover:text-gray-900 block px-3 py-2 text-base font-medium"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  Create Auction
                </Link>
              )}
            </div>
          </div>
        )}
      </header>

      {/* Main Content */}
      <main className="flex-1">
        {children}
      </main>

      {/* Footer */}
      <footer className="bg-white border-t border-gray-200">
        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div className="col-span-1 md:col-span-2">
              <div className="flex items-center space-x-2 mb-4">
                <Gavel className="h-8 w-8 text-blue-600" />
                <span className="text-xl font-bold text-gray-900">AuctionHub</span>
              </div>
              <p className="text-gray-600 text-sm">
                The premier online auction platform for buying and selling unique items.
                Join thousands of users in exciting auctions every day.
              </p>
            </div>

            <div>
              <h3 className="text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4">
                Quick Links
              </h3>
              <ul className="space-y-2">
                <li><Link href="/auctions" className="text-gray-600 hover:text-gray-900 text-sm">Browse Auctions</Link></li>
                <li><Link href="/categories" className="text-gray-600 hover:text-gray-900 text-sm">Categories</Link></li>
                <li><Link href="/how-it-works" className="text-gray-600 hover:text-gray-900 text-sm">How It Works</Link></li>
              </ul>
            </div>

            <div>
              <h3 className="text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4">
                Support
              </h3>
              <ul className="space-y-2">
                <li><Link href="/help" className="text-gray-600 hover:text-gray-900 text-sm">Help Center</Link></li>
                <li><Link href="/contact" className="text-gray-600 hover:text-gray-900 text-sm">Contact Us</Link></li>
                <li><Link href="/terms" className="text-gray-600 hover:text-gray-900 text-sm">Terms of Service</Link></li>
                <li><Link href="/privacy" className="text-gray-600 hover:text-gray-900 text-sm">Privacy Policy</Link></li>
              </ul>
            </div>
          </div>

          <div className="mt-8 pt-8 border-t border-gray-200">
            <p className="text-center text-gray-600 text-sm">
              © 2024 AuctionHub. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default AppLayout;
